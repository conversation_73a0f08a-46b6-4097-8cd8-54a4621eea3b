import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/models/branch_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/controllers/components/persistensi_filter_controller.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PersistensiFilter extends StatelessWidget {
  final List<String> selectedBranches;
  final Function(List<String>) onBranchesChanged;
  final VoidCallback onReset;
  final VoidCallback onApply;

  PersistensiFilter({
    super.key,
    required this.selectedBranches,
    required this.onBranchesChanged,
    required this.onReset,
    required this.onApply,
  });

  // Use GetX controller
  final PersistensiFilterController controller = Get.put(
    PersistensiFilterController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        padding: EdgeInsets.all(paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Branch Selection (show loading or branches)
            if (controller.isLoading.value) ...[
              Text(
                'Cabang',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              SizedBox(height: paddingSmall),
              Center(child: CircularProgressIndicator()),
            ] else if (controller.branches.isNotEmpty) ...[
              Text(
                'Cabang',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),

              SizedBox(height: paddingSmall),

              // Branch list with checkboxes
              Container(
                constraints: BoxConstraints(maxHeight: 200),
                child: SingleChildScrollView(
                  child: Column(
                    children:
                        controller.branches
                            .map((branch) => _buildBranchOption(branch))
                            .toList(),
                  ),
                ),
              ),
            ],

            SizedBox(height: paddingLarge),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: PdlButton(
                    onPressed: () {
                      // Clear all selections
                      onBranchesChanged([]);
                      // Auto submit after reset
                      onApply();
                      Get.back();
                    },
                    title: 'Reset',
                    backgroundColor: Colors.grey[300],
                    foregorundColor: Colors.black,
                  ),
                ),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: PdlButton(
                    onPressed: () {
                      onApply();
                      Get.back();
                    },
                    title: 'Terapkan',
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildBranchOption(BranchModels branch) {
    final isSelected = selectedBranches.contains(branch.branchCode);
    return Container(
      margin: EdgeInsets.only(bottom: paddingSmall),
      child: Row(
        children: [
          Checkbox(
            value: isSelected,
            onChanged: (bool? value) {
              List<String> newSelection = List.from(selectedBranches);
              if (value == true) {
                newSelection.add(branch.branchCode!);
              } else {
                newSelection.remove(branch.branchCode);
              }
              onBranchesChanged(newSelection);
            },
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                List<String> newSelection = List.from(selectedBranches);
                if (isSelected) {
                  newSelection.remove(branch.branchCode);
                } else {
                  newSelection.add(branch.branchCode!);
                }
                onBranchesChanged(newSelection);
              },
              child: Text(
                branch.branchName ?? '',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
