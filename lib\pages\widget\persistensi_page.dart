import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

import '../../base/base_detail_page.dart';
import '../../components/filter_button.dart';
import '../../components/persistensi_filter.dart';
import '../../components/pdl_text_field.dart';
import '../../controllers/widget/persistensi_page_controller.dart';
import '../../models/persistensi_model.dart';
import '../../routes/app_routes.dart';
import '../../utils/keys.dart';
import '../../utils/utils.dart';

class PersistensiPage extends StatefulWidget {
  const PersistensiPage({super.key});

  @override
  State<PersistensiPage> createState() => _PersistensiPageState();
}

class _PersistensiPageState extends State<PersistensiPage> {
  final PersistensiPageController controller = Get.put(
    PersistensiPageController(),
    tag: Utils.getRandomString(),
  );

  late TextEditingController searchController;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => BaseDetailPage(
        onRefresh: () => controller.refreshData(),
        backEnabled: true,
        controller: controller,
        scrollController: controller.teamController.scrollController,
        title: controller.getPageTitle(),
        child: Container(
          width: Get.width,
          padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
          child: Obx(() {
            if (controller.isLoading.isTrue) {
              return const Center(child: CircularProgressIndicator());
            }
            return Column(
              spacing: paddingMedium,
              children: [
                SizedBox(height: 10),
                // Show agent profile if viewing other agent
                if (controller.isShowOtherAgent) _buildAgentProfile(),

                // Show tabs based on channel and role
                if (controller.shouldShowTabs()) _buildTabSelector(context),

                // Show search and filter for team/group view
                if (controller.shouldShowTabs() &&
                    controller.selectedSection.value != 0)
                  _buildSearchAndFilter(),

                // Content based on role and selected tab
                _buildContent(),
              ],
            );
          }),
        ),
      ),
    );
  }

  // Tab selector based on available tabs
  Widget _buildTabSelector(BuildContext context) {
    final availableTabs = controller.getAvailableTabs();

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children:
              availableTabs.asMap().entries.map((entry) {
                final index = entry.key;
                final tabName = entry.value;
                final isSelected = controller.selectedSection.value == index;

                return Expanded(
                  child: InkWell(
                    onTap: () {
                      switch (index) {
                        case 0:
                          controller.switchToIndividu();
                          break;
                        case 1:
                          controller.switchToTeam();
                          break;
                        case 2:
                          controller.switchToArea();
                          break;
                      }
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(50),
                        border:
                            isSelected
                                ? Border.all(color: kColorBgLight)
                                : null,
                        color:
                            isSelected
                                ? Theme.of(context).colorScheme.surface
                                : null,
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        tabName,
                        style: TextStyle(
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // For BP role (AGE channel), show only individu content
    if (controller.channel != kUserChannelBan && controller.level == kLevelBP) {
      return _buildIndividuContent();
    }

    // For other roles, show content based on selected tab
    return Obx(() {
      switch (controller.selectedSection.value) {
        case 0:
          return _buildIndividuContent();
        case 1:
          return _buildTeamContent();
        case 2:
          return _buildAreaContent();
        default:
          return _buildIndividuContent();
      }
    });
  }

  // Individu Content
  Widget _buildIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasError.value;
      final errorMessage = controller.individuController.errorMessage.value;

      void onRetry() {
        controller.individuController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.individuController.persistensiData.value ==
                null)
              const Center(child: Text('No data available'))
            else
              _buildPersistencyData(
                controller.individuController.persistensiData.value!,
              ),
          ],
        ),
      );
    });
  }

  // Team Content
  Widget _buildTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasError.value;
      final errorMessage = controller.teamController.errorMessage.value;

      void onRetry() {
        controller.teamController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.teamController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAgentPersistencyTables(),

            // Load more indicator
            Obx(() {
              if (controller.teamController.isLoadingMore.value) {
                return const Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child: Center(child: CircularProgressIndicator()),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      );
    });
  }

  // Build persistency data display
  Widget _buildPersistencyData(PersistensiModel data) {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPersistenceRow(
          'Persistensi-13',
          controller.individuController.formatPercentage(data.persistency13),
        ),
        _buildPersistenceRow(
          'Persistensi-25',
          controller.individuController.formatPercentage(data.persistency25),
        ),
        _buildPersistenceRow(
          'Persistensi-37',
          controller.individuController.formatPercentage(data.persistency37),
        ),
        _buildPersistenceRow(
          'Persistensi-49',
          controller.individuController.formatPercentage(data.persistency49),
        ),
        _buildPersistenceRow(
          'Persistensi-61',
          controller.individuController.formatPercentage(data.persistency61),
        ),
      ],
    );
  }

  // Build single table for team/group persistency data
  Widget _buildAgentPersistencyTables() {
    final agents =
        (!controller.showFullData.value &&
                    controller.teamController.persistensiDataList.length > 3
                ? controller.teamController.persistensiDataList.take(3)
                : controller.teamController.persistensiDataList)
            .toList();

    if (agents.isEmpty) {
      return Container();
    }

    return _buildClickableTable(
      headers: ["name_str".tr, "P-13", "P-25", "P-37", "P-49", "P-61"],
      agents: agents,
      isTeamData: true,
    );
  }

  // Check if user can click agent names
  bool _canClickAgentNames() {
    // Only allow clicking for team tab (selectedSection == 1)
    if (controller.selectedSection.value != 1) return false;

    // Only allow for specific user levels: RSM, HOS, HOB
    final userLevel =
        controller.prefs.getString(kStorageUserLevelComplete) ?? '';
    return [
      kUserLevelBanRSM,
      kUserLevelBanHOS,
      kUserLevelBanHOB,
    ].contains(userLevel);
  }

  // Build clickable table with name as first column (same as widget)
  Widget _buildClickableTable({
    required List<String> headers,
    required List<PersistensiModel> agents,
    required bool isTeamData,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: paddingMedium),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radiusSmall),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            decoration: BoxDecoration(
              color: Color(0xFF0075BD),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(radiusSmall),
                topRight: Radius.circular(radiusSmall),
              ),
            ),
            child: Row(
              children:
                  headers.map((header) {
                    return Expanded(
                      flex:
                          header == headers.first ? 2 : 1, // Name column wider
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          header,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),

          // Data rows
          ...agents.map((agent) {
            if (agent.name == '') return Container();

            return Container(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300, width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  // Name column (conditionally clickable)
                  Expanded(
                    flex: 2,
                    child:
                        _canClickAgentNames()
                            ? InkWell(
                              onTap: () => _navigateToAgentDetail(agent),
                              child: Padding(
                                padding: const EdgeInsets.all(paddingSmall),
                                child: Text(
                                  agent.name,
                                  style: TextStyle(
                                    color: Colors.blue,
                                    decoration: TextDecoration.underline,
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                              ),
                            )
                            : Padding(
                              padding: const EdgeInsets.all(paddingSmall),
                              child: Text(
                                agent.name,
                                textAlign: TextAlign.left,
                              ),
                            ),
                  ),

                  // Persistency columns
                  if (isTeamData) ...[
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency13,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency25,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency37,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency49,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency61,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ] else ...[
                    // Area data (5 columns)
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency13,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency25,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency37,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency49,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency61,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  // Navigate to agent detail page
  void _navigateToAgentDetail(PersistensiModel agent) {
    // Determine mode based on current section
    int mode = controller.selectedSection.value;
    Get.toNamed(
      '${Routes.PERSISTENSI}?mode=$mode&agentCode=${agent.agentCode}',
    );
  }

  // Area Content (for Cabang view)
  Widget _buildAreaContent() {
    return Obx(() {
      final isLoading = controller.areaController.isLoading.value;
      final hasError = controller.areaController.hasError.value;
      final errorMessage = controller.areaController.errorMessage.value;

      void onRetry() {
        controller.areaController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.areaController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAreaPersistencyTables(),

            // Load more indicator
            Obx(() {
              if (controller.areaController.isLoadingMore.value) {
                return Center(child: CircularProgressIndicator());
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      );
    });
  }

  // Build single table for area persistency data
  Widget _buildAreaPersistencyTables() {
    final areas = controller.areaController.persistensiDataList;

    if (areas.isEmpty) {
      return Container();
    }

    return _buildClickableTable(
      headers: ["name_str".tr, "P-13", "P-25", "P-37", "P-49", "P-61"],
      agents: areas,
      isTeamData: false,
    );
  }

  Widget _buildPersistenceRow(String title, String percentage) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
        Text(
          percentage,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
      ],
    );
  }

  // Build agent profile section for other agent view
  Widget _buildAgentProfile() {
    return Obx(() {
      if (controller.isLoadingProfile.value) {
        return Padding(
          padding: EdgeInsets.all(paddingSmall),
          child: Center(child: CircularProgressIndicator()),
        );
      }

      final profile = controller.agentProfile.value;
      if (profile == null) {
        return Container();
      }

      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(radiusSmall),
        ),
        child: Row(
          children: [
            CircleAvatar(
              radius: 25,
              backgroundImage:
                  profile.photo != null && profile.photo!.isNotEmpty
                      ? NetworkImage(profile.photo!)
                      : null,
              child:
                  profile.photo == null || profile.photo!.isEmpty
                      ? Text(
                        Utils.getInitials(profile.name ?? ''),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                      : null,
            ),
            SizedBox(width: paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    profile.agentName ?? '',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '${profile.agentLevel ?? ''} - ${profile.agentCode ?? ''}',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                  if (profile.branchName != null)
                    Text(
                      profile.branchName!,
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  // Search and filter for team/group view
  Widget _buildSearchAndFilter() {
    return Column(
      children: [
        SizedBox(height: paddingSmall),
        Row(
          children: [
            Expanded(
              child: PdlTextField(
                textController: controller.searchController,
                hint: 'search_str'.tr,
                prefixIcon: Icon(Icons.search),
                enabled: true,
                textInputAction: TextInputAction.search,
                onChanged: (value) {
                  // Optional: implement real-time search
                },
                onEditingComplete: () {
                  controller.performSearch(controller.searchController.text);
                },
              ),
            ),
            SizedBox(width: paddingSmall),
            // Only show filter button for area/cabang tab (section 2)
            if (controller.selectedSection.value == 2)
              FilterButton(
                content: PersistensiFilter(
                  selectedBranches: controller.selectedBranches.toList(),
                  onBranchesChanged:
                      (branches) =>
                          controller.selectedBranches.assignAll(branches),
                  onReset: () => controller.resetFilter(),
                  onApply:
                      () => controller.applyBranchFilter(
                        controller.selectedBranches.toList(),
                      ),
                ),
                title: 'filter_str'.tr,
              ),
          ],
        ),
        SizedBox(height: paddingMedium),
      ],
    );
  }
}
