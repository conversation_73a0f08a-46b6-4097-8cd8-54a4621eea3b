import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/promosi_bm_tab_controller.dart';
import 'package:pdl_superapp/controllers/widget/validasi_ban_controller.dart';
import 'package:pdl_superapp/controllers/widget/validasi_bd_controller.dart';
import 'package:pdl_superapp/controllers/widget/validasi_tab_controller.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'dart:async';

class ValidasiWidgetController extends BaseControllers {
  final RxBool isExpanded = true.obs;
  final RxInt selectedSection = 0.obs; // 0 for Validasi, 1 for Promosi BM/BD
  String level = '';
  String channel = '';

  // Flag untuk lazy loading
  bool _hasInitialized = false;

  // StreamSubscription untuk lazy loading
  StreamSubscription<WidgetLoadEvent>? _loadEventSubscription;

  // Controllers
  late ValidasiTabController validasiController;
  late PromosiBMTabController promosiBMController;
  late ValidasiBDController validasiBDController;
  late ValidasiBanController validasiBanController;
  late SharedPreferences prefs;

  @override
  onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    channel = prefs.getString(kStorageUserChannel) ?? '';

    validasiController = Get.put(
      ValidasiTabController(),
      tag: Utils.getRandomString(),
    );
    promosiBMController = Get.put(
      PromosiBMTabController(),
      tag: Utils.getRandomString(),
    );
    validasiBDController = Get.put(
      ValidasiBDController(),
      tag: Utils.getRandomString(),
    );
    validasiBanController = Get.put(
      ValidasiBanController(),
      tag: Utils.getRandomString(),
    );

    final LazyLoadingController lazyLoadingController =
        Get.find<LazyLoadingController>();
    setupLazyLoading(lazyLoadingController);
    // Tidak langsung fetch data, tunggu trigger dari lazy loading
    _hasInitialized = true;
  }

  /// Method untuk setup lazy loading listener
  void setupLazyLoading(LazyLoadingController lazyLoadingController) {
    // Listen untuk load events dari LazyLoadingController
    try {
      _loadEventSubscription = lazyLoadingController.loadEventStream.listen((
        event,
      ) {
        if (event.widgetKey == 'validasi_widget') {
          initializeLazyLoading();
        }
      });
    } catch (e) {
      // LazyLoadingController belum ada, fallback ke loading biasa
      initializeLazyLoading();
    }
  }

  /// Method untuk trigger lazy loading dari LazyLoadingController
  Future<void> initializeLazyLoading() async {
    if (!_hasInitialized) {
      await _initialize();
    }
    refreshData();
  }

  @override
  void onClose() {
    _loadEventSubscription?.cancel();
    super.onClose();
  }

  /// Private initialization method
  Future<void> _initialize() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    channel = prefs.getString(kStorageUserChannel) ?? '';
    _hasInitialized = true;
  }

  // Toggle accordion expanded state
  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  // Switch between sections
  void switchToValidasi() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToPromosiBM() {
    selectedSection.value = 1;
    refreshData();
  }

  // Refresh data based on current section and user level
  void refreshData() {
    if (channel == "BAN") {
      validasiBanController.fetchValidasiData();
    } else if (level == kLevelBD) {
      validasiBDController.fetchValidasiData();
    } else if (selectedSection.value == 0) {
      validasiController.fetchValidasiData();
    } else {
      promosiBMController.fetchPromosiAgentData();
    }
    setLoading(false);
  }
}
