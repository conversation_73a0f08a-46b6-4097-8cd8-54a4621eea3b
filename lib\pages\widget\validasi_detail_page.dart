import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/table_card_ban.dart';
import 'package:pdl_superapp/controllers/widget/validasi_ban_controller.dart';
import 'package:pdl_superapp/models/validasi_ban_model.dart';
import 'package:pdl_superapp/utils/constants.dart';

class ValidasiDetailPage extends StatelessWidget {
  ValidasiDetailPage({super.key});

  final ValidasiBanController controller = Get.put(
    ValidasiBanController(),
    tag: "validasi-detail-page-${DateTime.timestamp()}",
  );

  @override
  Widget build(BuildContext context) {
    controller.fetchValidasiData();
    return BaseDetailPage(
      onRefresh: () => controller.fetchValidasiData(),
      backEnabled: true,
      controller: controller,
      title: 'validation_abu_system_str'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(paddingLarge),
                child: CircularProgressIndicator(),
              ),
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: paddingMedium,
            children: [
              const SizedBox(height: paddingSmall),
              if (controller.validasiData.value != null)
                TableCardBan(
                  isIndividu: true,
                  title: 'Individu',
                  data: ValidasiBanTeam(
                    agentCode: controller.validasiData.value!.agentCode,
                    agentName: controller.validasiData.value!.agentName,
                    agentPhoto: controller.validasiData.value!.agentPhoto,
                    agentLevel: controller.validasiData.value!.agentLevel,
                    result: controller.validasiData.value!.individu,
                  ),
                ),
              if (controller.validasiData.value == null)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Individu',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: paddingMedium),
                    Center(child: Text('No data available')),
                  ],
                ),

              Divider(height: paddingMedium),

              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Team',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: paddingMedium),
                  if (controller.validasiData.value != null &&
                      controller.validasiData.value!.team.isNotEmpty)
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: controller.validasiData.value!.team.length,
                      padding: EdgeInsets.zero,
                      itemBuilder: (context, index) {
                        final row = controller.validasiData.value!.team[index];
                        return TableCardBan(data: row);
                      },
                      separatorBuilder:
                          (context, index) => SizedBox(height: paddingMedium),
                    ),
                  if (controller.validasiData.value != null &&
                      controller.validasiData.value!.team.isEmpty)
                    Center(child: Text('No data available')),
                ],
              ),

              const SizedBox(height: paddingExtraLarge),
            ],
          );
        }),
      ),
    );
  }
}
