import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/status_label.dart';
import 'package:pdl_superapp/models/response/list_approval_response.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class KeagenanController extends BaseControllers {
  RxList<ApprovalDataModel> approvalRejoinList = <ApprovalDataModel>[].obs;
  RxList<ApprovalDataModel> approvalTerminasiList = <ApprovalDataModel>[].obs;
  RxList<ApprovalDataModel> approvalTerminasiHistoryList =
      <ApprovalDataModel>[].obs;
  RxList<ApprovalDataModel> approvalRecruitmentList = <ApprovalDataModel>[].obs;
  RxList<ApprovalDataModel> approvalRecruitmentHistoryList =
      <ApprovalDataModel>[].obs;

  // Status loading
  RxBool isLoadingForms = false.obs;
  RxBool isLoadingApiData = false.obs;
  RxBool isLoadingListApproval = false.obs;
  RxBool isLoadingListApprovalHistory = false.obs;

  // Tab recruitment
  RxInt activeRecruitmentTabIndex = 0.obs;

  RxList<String> availableFilterStatus = <String>[].obs;
  RxList<String> availableFilterView = <String>['Individu', 'Team'].obs;

  //Filter dialog
  final filterView = Rxn<String>();
  RxString filterStatus = ''.obs;

  // Filter pencarian
  RxString searchQuery = ''.obs;
  final q = Rxn<String>();

  // Productivity chart data
  RxList<FlSpot> chartData = <FlSpot>[].obs;
  RxList<FlSpot> chartDataBerlisensi = <FlSpot>[].obs;
  RxDouble maxY = 0.0.obs;
  RxDouble minY = 0.0.obs;
  RxBool isMonthlyView = false.obs;
  RxString currentFilterType = kSwitchYearly.obs;

  // Chart labels
  List<String> years = [];
  List<String> months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];

  // Productivity summary data
  RxInt totalRecruitments = 0.obs;
  RxInt rekrutBerkodeAgen = 0.obs;
  RxInt rekrutBaruBerlisensi = 0.obs;
  RxString period = ''.obs;
  RxDouble percentageRekrutBerkodeAgen = 0.0.obs;
  RxDouble percentageRekrutBaruBerlisensi = 0.0.obs;

  // Filter properties
  RxString selectedView = 'Individu'.obs; // Individu/Team
  RxString selectedPeriode = 'Pertahun'.obs; // Perbulan/Pertahun
  RxString selectedRekrut = 'Berkode Agen'.obs; // Berkode Agen/Baru Berlisensi
  RxString selectedChart = 'Line Chart'.obs; // Line Chart/Bar Chart

  // Chart color getter
  Color get chartColor {
    if (selectedPeriode.value == 'Perbulan') {
      return kColorGlobalGreen;
    } else {
      return kColorGlobalWarning;
    }
  }

  // Get active filter count (filters that are not default)
  int get activeFilterCount {
    int count = 0;

    // Default values
    const defaultView = 'Individu';
    const defaultPeriode = 'Pertahun';
    const defaultRekrut = 'Berkode Agen';
    const defaultChart = 'Line Chart';

    if (selectedView.value != defaultView) count++;
    if (selectedPeriode.value != defaultPeriode) count++;
    if (selectedRekrut.value != defaultRekrut) count++;
    if (selectedChart.value != defaultChart) count++;

    return count;
  }

  late SharedPreferences prefs;
  String level = '';
  RxString selectedMenuCandidates = 'Rekrut'.obs;
  RxString currentAgentCode = ''.obs;
  List<Map<String, dynamic>> levelApprovalRejoinList = [];
  List<Map<String, dynamic>> menuCandidateCard = [];

  // Pagination properties
  RxInt totalPages = 0.obs;
  RxInt currentPage = 0.obs;
  RxInt totalElements = 0.obs;
  RxBool hasNextPage = false.obs;
  RxBool isLoadingMore = false.obs;

  void resetFilter() {
    filterView.value = null;
    filterStatus.value = availableFilterStatus.first;
    getListData(isRefresh: true);
    Get.back();
  }

  void getApprovalRejoinLevelAvailables() {
    if (currentAgentCode.value == kLevelBP) {
      levelApprovalRejoinList = [
        {'code': 'BP', 'name': 'Business Partner'},
      ];
    } else {
      levelApprovalRejoinList = [
        {'code': 'BP', 'name': 'Business Partner'},
        {'code': 'BM', 'name': 'Business Manager'},
      ];
    }
  }

  void setMenuCandidatesCard() {
    menuCandidateCard = [
      {
        'title': 'Rekrut',
        'startColor': Color(0xFFE6CFFF),
        'endColor': Color(0xFFBA83F6),
      },
      {
        'title': 'Promosi',
        'startColor': Color(0xFFF6D436),
        'endColor': Color(0xFFFFA751),
      },
      {
        'title': 'Demosi',
        'startColor': Color(0xFFF6D436),
        'endColor': Color(0xFFFFA751),
      },
      {
        'title': 'Terminasi',
        'startColor': Color.fromARGB(255, 246, 134, 167),
        'endColor': Color.fromARGB(255, 242, 61, 76),
      },
      {
        'title': 'Bergabung Kembali',
        'startColor': kColorGlobalBlue,
        'endColor': kColorGlobalBlue100,
      },
    ];
  }

  List<String> rejoinLevelAvailables() {
    if (level == kLevelBP) {
      return [];
    } else if (level == kLevelBM) {
      return ['BP'];
    } else {
      return ['BP', 'BM'];
    }
  }

  void _setFilterStatus() {
    availableFilterStatus.add('Semua');
    for (final item in Status.values) {
      if (item.name == 'DRAFT') continue;
      if (item.name == 'EXPIRED') continue;
      availableFilterStatus.add(item.name);
    }
  }

  @override
  Future<void> onInit() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    currentAgentCode.value = prefs.getString(kStorageAgentCode) ?? '';
    super.onInit();
    fetchDataGrafik();
    _setFilterStatus();
    getApprovalRejoinLevelAvailables();
    setMenuCandidatesCard();
    getListData(isRefresh: true);

    filterStatus.value = availableFilterStatus.first;
    filterView.value = availableFilterView.first;
  }

  void getListData({bool isRefresh = false}) {
    switch (selectedMenuCandidates.value) {
      case 'Rekrut':
        _getApproval(isRefresh: isRefresh);
        break;
      case 'Promosi':
        break;
      case 'Demosi':
        break;
      case 'Terminasi':
        _getApproval(isRefresh: isRefresh);
        break;
      case 'Bergabung Kembali':
        _getApproval(isRefresh: isRefresh);
        break;
      default:
    }
  }

  void _getApproval({bool isRefresh = false}) {
    if (activeRecruitmentTabIndex.value == 0) {
      fetchApiListApproval(isRefresh: isRefresh);
    } else {
      fetchApiListApprovalHistory(isRefresh: isRefresh);
    }
  }

  void changeActiveRecruitmentTab(int active) {
    activeRecruitmentTabIndex.value = active;
    getListData(isRefresh: true);
  }

  // Load more data for pagination
  void loadMoreData() {
    if (hasNextPage.value && !isLoadingMore.value) {
      currentPage.value += 1;
      getListData(isRefresh: false);
    }
  }

  Future<void> fetchDataGrafik() async {
    final params = _buildApiParams();
    await api.getProductivityData(controllers: this, params: params);
  }

  // Build API parameters based on selected filters
  String _buildApiParams() {
    final currentYear = DateTime.now().year;

    // Base parameters
    String params = 'year=$currentYear';

    // View filter (scope)
    if (selectedView.value == 'Individu') {
      params += '&scope=SELF';
    } else {
      params += '&scope=TEAM';
    }

    // Periode filter (groupBy)
    if (selectedPeriode.value == 'Perbulan') {
      params += '&groupBy=MONTHLY';
      currentFilterType.value = kSwitchMonthly;
    } else {
      params += '&groupBy=YEARLY';
      currentFilterType.value = kSwitchYearly;
    }

    // Rekrut filter (recruitmentType)
    if (selectedRekrut.value == 'Berkode Agen') {
      params += '&recruitmentType=REKRUT_BERKODE_AGEN';
    } else {
      params += '&recruitmentType=REKRUT_BARU_BERLISENSI';
    }

    return params;
  }

  Future<void> fetchApiListApproval({bool isRefresh = false}) async {
    // Prevent multiple simultaneous requests
    if (isLoadingListApproval.value && !isRefresh) return;

    if (isRefresh) {
      isLoadingListApproval.value = true;
      setLoading(true);
      currentPage.value = 0;
      totalPages.value = 0;
      totalElements.value = 0;
      hasNextPage.value = false;
      approvalRejoinList.clear();
      approvalTerminasiList.clear();
      approvalTerminasiHistoryList.clear();
      approvalRecruitmentList.clear();
      approvalRecruitmentHistoryList.clear();
    } else {
      // Loading more data
      if (!hasNextPage.value) return; // No more data to load
      isLoadingMore.value = true;
    }

    String params = '?page=${currentPage.value}&size=20&sort=createdAt,DESC';

    if (q.value != null && q.value!.trim().isNotEmpty) {
      params = '$params&q=${q.value}';
    }
    if (filterStatus.value != 'Semua') {
      params =
          '$params&trxStatus=${filterStatus.value.replaceAll(' ', '_').toUpperCase()}';
      // '$params&approvalStatus=${filterStatus.value.replaceAll(' ', '_').toUpperCase()}';
    }

    String codeTrxType = '';
    switch (selectedMenuCandidates.value) {
      case 'Rekrut':
        codeTrxType = 'RECRUITMENT_';
        break;
      case 'Promosi':
        codeTrxType = 'PROMOSI_';
        break;
      case 'Terminasi':
        codeTrxType = 'TERMINASI_';
        break;
      case 'Demosi':
        codeTrxType = 'DEMOSI_';
        break;
      case 'Bergabung Kembali':
        codeTrxType = 'REJOIN_';
        break;
      default:
    }
    if (selectedMenuCandidates.value == 'Rekrut') {
      params =
          '$params&trxType=RECRUITMENT_BM&trxType=RECRUITMENT_BP&trxType=RECRUITMENT_BD';
    } else {
      for (final item in levelApprovalRejoinList) {
        params = '$params&trxType=$codeTrxType${item['code']}';
      }
    }

    try {
      await api.getListApproval(
        controllers: this,
        params: params,
        code: kReqListApprovalRejoin,
      );
    } catch (e) {
      try {
        Get.find<LoggerService>().log(
          'Error fetching API recruitment list: $e',
        );
      } catch (_) {
        log('Error fetching API recruitment list: $e');
      }
    }
  }

  Future<void> fetchApiListApprovalHistory({bool isRefresh = false}) async {
    // Prevent multiple simultaneous requests
    if (isLoadingListApprovalHistory.value && !isRefresh) return;

    if (isRefresh) {
      isLoadingListApprovalHistory.value = true;
      setLoading(true);
      currentPage.value = 0;
      totalPages.value = 0;
      totalElements.value = 0;
      hasNextPage.value = false;
      approvalTerminasiList.clear();
      approvalTerminasiHistoryList.clear();
      approvalRejoinList.clear();
      approvalRecruitmentList.clear();
      approvalRecruitmentHistoryList.clear();
    } else {
      // Loading more data
      if (!hasNextPage.value) return; // No more data to load
      isLoadingMore.value = true;
    }

    String params = '?page=${currentPage.value}&size=20&sort=createdAt,DESC';

    if (q.value != null && q.value!.trim().isNotEmpty) {
      params = '$params&q=${q.value}';
    }
    if (filterStatus.value != 'Semua') {
      params =
          '$params&trxStatus=${filterStatus.value.replaceAll(' ', '_').toUpperCase()}';
      // '$params&approvalStatus=${filterStatus.value.replaceAll(' ', '_').toUpperCase()}';
    }

    if (filterView.value != null) {
      if (filterView.value == 'Individu') {
        params = '$params&view=INDIVIDUAL';
      } else if (filterView.value == 'Team') {
        params = '$params&view=TEAM';
      }
    }

    String codeTrxType = '';
    switch (selectedMenuCandidates.value) {
      case 'Rekrut':
        codeTrxType = 'RECRUITMENT_';
        break;
      case 'Promosi':
        codeTrxType = 'PROMOSI_';
        break;
      case 'Terminasi':
        codeTrxType = 'TERMINASI_';
        break;
      case 'Demosi':
        codeTrxType = 'DEMOSI_';
        break;
      case 'Bergabung Kembali':
        codeTrxType = 'REJOIN_';
        break;
      default:
    }
    if (selectedMenuCandidates.value == 'Rekrut') {
      params =
          '$params&trxType=RECRUITMENT_BM&trxType=RECRUITMENT_BP&trxType=RECRUITMENT_BD';
    } else {
      for (final item in levelApprovalRejoinList) {
        params = '$params&trxType=$codeTrxType${item['code']}';
      }
    }

    try {
      await api.getListApprovalHistory(
        controllers: this,
        params: params,
        code: kReqListApprovalHistory,
      );
    } catch (e) {
      try {
        Get.find<LoggerService>().log(
          'Error fetching API recruitment history list: $e',
        );
      } catch (_) {
        log('Error fetching API recruitment history list: $e');
      }
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    isLoadingMore.value = false;
    if (requestCode == kReqListApprovalRejoin) {
      isLoadingListApproval.value = false;
    }
    if (requestCode == kReqListApprovalHistory) {
      isLoadingListApprovalHistory.value = false;
    }
    if (response.body == null) return;
    toastError(
      title: 'Gagal',
      message:
          response.body['message'] ??
          '${response.body['error_description'] ?? 'Terjadi Kesalahan harap ulangi kembali'}',
    );
  }

  void toastError({required String title, required String message}) {
    Get.snackbar(
      title,
      message,
      colorText: Colors.white,
      backgroundColor: Colors.red,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Handle API response
  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case 0: // Default code for productivity data
        parseProductivityData(response);
      case kReqListApprovalRejoin:
        isLoadingListApproval.value = false;
        isLoadingMore.value = false;
        _onSucessGetListApproval(response);
        break;
      case kReqListApprovalHistory:
        isLoadingListApprovalHistory.value = false;
        isLoadingMore.value = false;
        _onSucessGetListApprovalHistory(response);
        break;
      default:
        break;
    }
  }

  void _onSucessGetListApproval(response) {
    try {
      final resp = ListApprovalResponse.fromJson(response);

      // Update pagination info
      totalPages.value = resp.totalPages ?? 0;
      totalElements.value = resp.totalElements ?? 0;
      hasNextPage.value = !(resp.last ?? true);

      if (selectedMenuCandidates.value == 'Rekrut') {
        approvalRecruitmentList.addAll(resp.content ?? []);
        approvalRecruitmentList.refresh();
        return;
      }
      if (selectedMenuCandidates.value == 'Terminasi') {
        approvalTerminasiList.addAll(resp.content ?? []);
        approvalTerminasiList.refresh();
        return;
      }
      approvalRejoinList.addAll(resp.content ?? []);
      approvalRejoinList.refresh();
    } catch (e, st) {
      log('error success upload doc rejoin $e $st');
    }
  }

  void _onSucessGetListApprovalHistory(response) {
    try {
      final resp = ListApprovalResponse.fromJson(response);

      // Update pagination info
      totalPages.value = resp.totalPages ?? 0;
      totalElements.value = resp.totalElements ?? 0;
      hasNextPage.value = !(resp.last ?? true);

      if (selectedMenuCandidates.value == 'Rekrut') {
        approvalRecruitmentHistoryList.addAll(resp.content ?? []);
        approvalRecruitmentHistoryList.refresh();
        return;
      }
      if (selectedMenuCandidates.value == 'Terminasi') {
        approvalTerminasiHistoryList.addAll(resp.content ?? []);
        approvalTerminasiHistoryList.refresh();
        return;
      }
      approvalRejoinList.addAll(resp.content ?? []);
      approvalRejoinList.refresh();
    } catch (e, st) {
      log('error success get list approval history $e $st');
    }
  }

  // Parse productivity data response
  void parseProductivityData(dynamic response) {
    try {
      if (response == null) return;

      // Parse summary data
      if (response['summary'] != null) {
        final summary = response['summary'];
        totalRecruitments.value = summary['totalRecruitments'] ?? 0;
        rekrutBerkodeAgen.value = summary['rekrutBerkodeAgen'] ?? 0;
        rekrutBaruBerlisensi.value = summary['rekrutBaruBerlisensi'] ?? 0;
        period.value = summary['period'] ?? '';
        percentageRekrutBerkodeAgen.value =
            (summary['percentageRekrutBerkodeAgen'] ?? 0.0).toDouble();
        percentageRekrutBaruBerlisensi.value =
            (summary['percentageRekrutBaruBerlisensi'] ?? 0.0).toDouble();
      }

      // Parse chart data
      if (response['chartData'] != null) {
        final chartDataList = response['chartData'] as List;
        chartDataBerlisensi.clear();
        chartData.clear();
        years.clear();

        double maxValue = 0;
        double minValue = double.infinity;

        for (int i = 0; i < chartDataList.length; i++) {
          final item = chartDataList[i];
          final total = (item['rekrutBerkodeAgen'] ?? 0).toDouble();
          chartData.add(FlSpot(i.toDouble(), total));

          final totalBerlisensi =
              (item['rekrutBaruBerlisensi'] ?? 0).toDouble();
          chartDataBerlisensi.add(FlSpot(i.toDouble(), totalBerlisensi));
          years.add(item['label'] ?? '');

          if (total > maxValue) maxValue = total;
          if (total < minValue && total > 0) minValue = total;
        }

        // Set chart bounds
        maxY.value = maxValue > 0 ? maxValue * 1.2 : 10;
        minY.value = 0;
      }

      log('Successfully parsed productivity data');
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error parsing productivity data: $e');
      } catch (_) {
        log('Error parsing productivity data: $e');
      }
    }
  }

  // Update filter pencarian
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  // Refresh data
  Future<void> refreshData() async {
    await Future.wait([
      fetchDataGrafik(),
      // fetchDraftForms(),
      // fetchApiRecruitmentList(),
      fetchApiListApproval(isRefresh: true),
    ]);
  }

  // Navigasi ke halaman form untuk melanjutkan pengisian
  void continueForm(String formId) async {
    await Get.toNamed(Routes.KEAGENAN_FORM, parameters: {'formId': formId});
    // Refresh data when returning from form
    await refreshData();
  }

  // Navigasi ke halaman approval
  void goToApproval(String uuid) {
    Get.toNamed(Routes.APPROVAL, arguments: {'uuid': uuid});
  }

  // Format currency for chart labels
  String formatCurrency(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(0)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }

  // Apply filters and refresh chart data
  void applyFilters() {
    fetchDataGrafik();
  }

  // Reset filters to default
  void resetFilters() {
    selectedView.value = 'Individu';
    selectedPeriode.value = 'Pertahun';
    selectedRekrut.value = 'Berkode Agen';
    selectedChart.value = 'Line Chart';
    applyFilters();
  }
}
