import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/persistensi_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PersistensiTeamController extends BaseControllers {
  // Changed to store a list of persistence data for multiple agents
  RxList<PersistensiModel> persistensiDataList = <PersistensiModel>[].obs;
  late SharedPreferences prefs;

  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  ScrollController scrollController = ScrollController();

  // Pagination properties
  int page = 0;
  int maxPage = 0;
  int limit = 50;
  bool enableLoadMore = true;
  RxBool isLoadingMore = false.obs;

  @override
  void onInit() {
    super.onInit();
    setupScrollListener();
  }

  void setupScrollListener() {
    scrollController.addListener(() {
      if (scrollController.position.atEdge) {
        if (scrollController.position.pixels == 0) {
          // You're at the top.
        } else {
          // You're at the bottom.
          loadMore();
        }
      }
    });
  }

  Future<void> loadMore() async {
    if (!enableLoadMore || isLoadingMore.value) return;
    if (page != maxPage) {
      fetchPersistensiData(isRefresh: false);
    }
  }

  fetchPersistensiData({bool isRefresh = true, String? searchQuery}) async {
    if (isRefresh) {
      setLoading(true);
      page = 0;
      maxPage = 0;
      enableLoadMore = true;
      persistensiDataList.clear();
    } else {
      isLoadingMore.value = true;
    }

    hasError.value = false;
    errorMessage.value = '';

    prefs = await SharedPreferences.getInstance();
    String agentCode = prefs.getString(kStorageAgentCode) ?? '';
    // Get agent code from URL parameter if available
    if (Get.parameters.containsKey('agentCode')) {
      agentCode = Get.parameters['agentCode'] ?? '';
    }

    final currentYear = DateTime.now().year.toString();

    try {
      String params =
          "agentCode=$agentCode&year=$currentYear&page=$page&size=$limit";

      // Add search query if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        params += "&q=$searchQuery";
      }

      await api.getPersistencyTeam(
        controllers: this,
        code: kReqGetPersistencyTeam,
        params: params,
      );
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Error fetching data: $e';
      setLoading(false);
      isLoadingMore.value = false;
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Parse pagination info
    parsePagination(response);
    parseData(response);

    setLoading(false);
    isLoadingMore.value = false;
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = response.statusText ?? 'Error loading data';
    setLoading(false);
    isLoadingMore.value = false;
  }

  void parsePagination(response) {
    if (response['last'] == false) {
      page = page + 1;
    } else {
      enableLoadMore = response['last'] == false;
    }
    maxPage = response['totalPages'] ?? 0;
  }

  void parseData(response) {
    if (response == null) {
      hasError.value = true;
      errorMessage.value = 'No data available';
      return;
    }

    try {
      // Clear lists if this is the first page
      if (response['first'] == true) {
        persistensiDataList.clear();
      }

      // Add new data to existing list
      if (response['content'] != null) {
        for (var item in response['content']) {
          persistensiDataList.add(PersistensiModel.fromJson(item));
        }
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Error parsing data: $e';
    }
  }

  // Helper method to format percentage values
  String formatPercentage(double value) {
    return '${value.toStringAsFixed(2)}%';
  }
}
