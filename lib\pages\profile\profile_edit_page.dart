import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/profile/profile_edit_controller.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ProfileEditPage extends StatelessWidget {
  ProfileEditPage({super.key});

  final ProfileEditController controller = Get.put(ProfileEditController());

  @override
  Widget build(BuildContext context) {
    final bool isWideScreen = kIsWeb;

    // Initialize data from arguments or let controller load from API
    if (Get.arguments != null) {
      UserModels userDatas = Get.arguments as UserModels;
      controller.userData.value = userDatas;
      controller.setInitialData(userDatas);
    }
    // Note: If no arguments provided, the controller will automatically load
    // user profile data in its onInit() and load() methods

    return Obx(
      () => BaseDetailPage(
        backEnabled: true,
        controller: controller,
        onRefresh: () => controller.loadUserProfile(),
        title:
            controller.isEdit.value
                ? 'label_edit_profile'.tr
                : 'label_my_profile'.tr,
        bottomAction:
            controller.isEdit.isTrue
                ? () => controller.checkAttachement(context)
                : null,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: paddingLarge),
              GestureDetector(
                onTap: () {
                  // If canceling edit mode, clear preview for non-BP/BM/BD users
                  if (controller.isEdit.value) {
                    controller.clearPreviewImage();
                  }
                  controller.isEdit.value = !controller.isEdit.value;
                },
                child: Obx(
                  () => TitleWidget(
                    title:
                        controller.isEdit.value
                            ? 'label_edit_profile'.tr
                            : 'label_my_profile'.tr,
                    action: Obx(
                      () =>
                          controller.isEdit.isTrue
                              ? Text(
                                'cancel'.tr,
                                style: Theme.of(
                                  context,
                                ).textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).primaryColor,
                                ),
                              )
                              : Wrap(
                                crossAxisAlignment: WrapCrossAlignment.center,
                                children: [
                                  Text(
                                    'sub_title_edit'.tr,
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium?.copyWith(
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                  SizedBox(width: paddingSmall),
                                  Utils.cachedSvgWrapper(
                                    'icon/ic-linear-pen-new-square.svg',
                                    color: Theme.of(context).primaryColor,
                                    height: 15,
                                  ),
                                ],
                              ),
                    ),
                  ),
                ),
              ),
              Container(
                width: Get.width,
                padding: EdgeInsets.all(paddingLarge),
                child: Center(
                  child: Stack(
                    children: [
                      GestureDetector(
                        onTap: () => controller.profileBottomSheet(context),
                        child: Container(
                          width: 90,
                          height: 90,
                          clipBehavior: Clip.hardEdge,
                          decoration: BoxDecoration(
                            color: kColorGlobalBgGreen,
                            borderRadius: BorderRadius.circular(90),
                          ),
                          child: Obx(() {
                            // Check if user is non-BP/BM/BD and has preview image
                            bool isNonAgentUser =
                                controller.userLevel.value != kUserLevelBp &&
                                controller.userLevel.value != kUserLevelBm &&
                                controller.userLevel.value != kUserLevelBd;

                            if (isNonAgentUser &&
                                controller
                                    .previewProfilePictureUrl
                                    .value
                                    .isNotEmpty) {
                              // Show preview for non-BP/BM/BD users
                              return CachedNetworkImage(
                                imageUrl:
                                    controller.previewProfilePictureUrl.value,
                                fit: BoxFit.cover,
                                alignment: Alignment.center,
                              );
                            } else if (controller.userData.value.photo !=
                                null) {
                              // Show original profile photo
                              return CachedNetworkImage(
                                imageUrl: controller.userData.value.photo!,
                                fit: BoxFit.cover,
                                alignment: Alignment.center,
                              );
                            } else {
                              // Show initials
                              return Center(
                                child: Text(
                                  Utils.getInitials(
                                    controller.userData.value.agentName ??
                                        controller.userData.value.name ??
                                        '-',
                                  ),
                                  style: Theme.of(
                                    context,
                                  ).textTheme.headlineLarge?.copyWith(
                                    color: kColorTextTersierLight,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              );
                            }
                          }),
                        ),
                      ),
                      if (controller.isEdit.isTrue)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: () => controller.profileBottomSheet(context),
                            child: Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface,
                                borderRadius: BorderRadius.circular(32),
                                border: Border.all(
                                  color:
                                      Get.isDarkMode
                                          ? kColorBorderDark
                                          : kColorBorderLight,
                                ),
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(6),
                                child: Utils.cachedSvgWrapper(
                                  color:
                                      Get.isDarkMode
                                          ? kColorTextDark
                                          : kColorTextLight,
                                  'icon/ic-linear-pen-new-square.svg',
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: paddingSmall),
              if (controller.inApprovalList.isNotEmpty)
                _messageEditInApproval(context),
              isWideScreen
                  ? _webForm(context, userData: controller.userData.value)
                  : _mobileForm(context, userData: controller.userData.value),
            ],
          ),
        ),
      ),
    );
  }

  Container _messageEditInApproval(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: paddingMedium),
      width: double.infinity,
      decoration: BoxDecoration(
        color: kColorGlobalBgRed,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Text(
        'info_edit_profile_wip'.tr,
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(color: kColorGlobalRed),
      ),
    );
  }

  Widget _webForm(context, {required UserModels userData}) {
    bool isBpBmBdRole = [kLevelBP, kLevelBM, kLevelBD].contains(userData.level);

    return Column(
      children: [
        if (isBpBmBdRole) ...[
          // Fields for BP, BM, BD roles
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'label_form_full_name'.tr,
              intialValue: userData.agentName,
              textController: controller.nameTextController,
              fieldName: kFieldagentName,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'label_form_email'.tr,
              intialValue: userData.email,
              textController: controller.emailTextController,
              fieldName: kFieldemail,
            ),
          ),
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'label_form_phone'.tr,
              intialValue: userData.phoneNumber,
              textController: controller.phoneTextController,
              fieldName: kFieldphoneNumber,
              isPhoneNumber: true,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'label_form_agent_code'.tr,
              intialValue: userData.agentCode,
              isRestricted: true,
              textController: controller.agentCodeTextController,
              fieldName: kFieldagentCode,
            ),
          ),
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'label_form_branch_code'.tr,
              intialValue: userData.branchCode,
              isRestricted: true,
              textController: controller.branchCodeTextController,
              fieldName: kFieldbranchCode,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'label_form_level'.tr,
              intialValue: userData.level,
              isRestricted: true,
              textController: controller.levelTextController,
              fieldName: kFieldlevel,
            ),
          ),
          _webFormWrapper(
            leftContent: Obx(() {
              if (controller.state.value != ControllerState.loading &&
                  controller.listBank.isNotEmpty) {
                return _dropDownWrapper(
                  context,
                  userData: userData,
                  label: 'label_form_bank'.tr,
                  item: controller.listBank,
                  selectedItem: controller.getBankDisplayValue(
                    controller.bankTextController.text,
                  ),
                  enabled: controller.isEdit.isTrue,
                  onChanged: (val) {
                    controller.bankTextController.text = val;
                  },
                  fieldName: kFieldbank,
                );
              }
              return Utils.shimmerLoad();
            }),
            rightContent: _textFieldWrapper(
              context,
              label: 'label_form_bank_number'.tr,
              intialValue: userData.bankAccountNumber,
              textController: controller.bankNumberTextController,
              fieldName: kFieldbankAccountNumber,
              keyboardType: TextInputType.numberWithOptions(),
            ),
          ),
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'label_form_address'.tr,
              intialValue: userData.address,
              isTextArea: true,
              textController: controller.addressTextController,
              fieldName: kFieldaddress,
            ),
            rightContent: Obx(() {
              if (controller.state.value != ControllerState.loading &&
                  controller.listStatus.isNotEmpty) {
                return _dropDownWrapper(
                  context,
                  userData: userData,
                  label: 'label_form_marital'.tr,
                  item: controller.listStatus,
                  selectedItem: controller.getMaritalStatusDisplayValue(
                    controller.maritalTextController.text,
                  ),
                  enabled: controller.isEdit.isTrue,
                  onChanged: (val) {
                    controller.maritalTextController.text = val;
                  },
                  fieldName: kFieldmaritalStatus,
                );
              }
              return Utils.shimmerLoad();
            }),
          ),
          _webFormWrapper(
            leftContent: Obx(() {
              if (controller.state.value != ControllerState.loading &&
                  controller.listPendidikan.isNotEmpty) {
                return _dropDownWrapper(
                  context,
                  userData: userData,
                  label: 'label_form_education'.tr,
                  item: controller.listPendidikan,
                  selectedItem: controller.getEducationDisplayValue(
                    controller.educationTextController.text,
                  ),
                  enabled: controller.isEdit.isTrue,
                  onChanged: (val) {
                    controller.educationTextController.text = val;
                  },
                  fieldName: kFieldeducation,
                );
              }
              return Utils.shimmerLoad();
            }),
            rightContent: Container(),
          ),
        ] else ...[
          // Fields for other roles
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'Nama lengkap',
              intialValue: userData.name,
              textController: controller.fullNameTextController,
              fieldName: 'name',
              isRestricted: true,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'Username',
              intialValue: userData.username,
              textController: controller.usernameTextController,
              fieldName: 'username',
              isRestricted: true,
            ),
          ),
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'Channel',
              intialValue: userData.channel,
              textController: controller.channelTextController,
              fieldName: 'channel',
              isRestricted: true,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'Jabatan',
              intialValue: userData.roles?.name,
              textController: controller.roleNameTextController,
              fieldName: 'roles.name',
              isRestricted: true,
            ),
          ),
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'KCU / KCP / Cabang',
              intialValue: '',
              textController: controller.branchNameTextController,
              fieldName: 'branchName',
              isRestricted: true,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'Status',
              intialValue: 'aktif',
              textController: controller.statusTextController,
              fieldName: 'status',
              isRestricted: true,
            ),
          ),
        ],
      ],
    );
  }

  Row _webFormWrapper({
    required Widget leftContent,
    required Widget rightContent,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(child: leftContent),
        SizedBox(width: paddingMedium),
        Expanded(child: rightContent),
      ],
    );
  }

  Widget _mobileForm(context, {required UserModels userData}) {
    bool isBpBmBdRole = [kLevelBP, kLevelBM, kLevelBD].contains(userData.level);

    return Column(
      children: [
        if (isBpBmBdRole || userData.channel == 'BAN') ...[
          // Fields for BP, BM, BD roles
          _textFieldWrapper(
            context,
            label: 'label_form_full_name'.tr,
            intialValue: 'label_form_full_name'.tr,
            textController: controller.nameTextController,
            fieldName: kFieldagentName,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_email'.tr,
            intialValue: 'label_form_email'.tr,
            textController: controller.emailTextController,
            fieldName: kFieldemail,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_phone'.tr,
            intialValue: 'label_form_phone'.tr,
            textController: controller.phoneTextController,
            fieldName: kFieldphoneNumber,
            isPhoneNumber: true,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_agent_code'.tr,
            intialValue: 'label_form_agent_code'.tr,
            isRestricted: true,
            textController: controller.agentCodeTextController,
            fieldName: kFieldagentCode,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_branch_code'.tr,
            intialValue: 'label_form_branch_code'.tr,
            isRestricted: true,
            textController: controller.branchCodeTextController,
            fieldName: kFieldbranchCode,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_level'.tr,
            intialValue: 'label_form_level'.tr,
            isRestricted: true,
            textController: controller.levelTextController,
            fieldName: kFieldlevel,
          ),
          Obx(() {
            if (controller.state.value != ControllerState.loading &&
                controller.listBank.isNotEmpty) {
              return _dropDownWrapper(
                context,
                userData: userData,
                label: 'label_form_bank'.tr,
                item: controller.listBank,
                selectedItem: controller.getBankDisplayValue(
                  controller.bankTextController.text,
                ),
                enabled: controller.isEdit.isTrue,
                onChanged: (val) {
                  controller.bankTextController.text = val;
                },
                fieldName: kFieldbank,
              );
            }
            return Utils.shimmerLoad();
          }),
          _textFieldWrapper(
            context,
            label: 'label_form_bank_number'.tr,
            intialValue: 'label_form_bank_number'.tr,
            textController: controller.bankNumberTextController,
            fieldName: kFieldbankAccountNumber,
            keyboardType: TextInputType.numberWithOptions(),
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_address'.tr,
            intialValue: 'label_form_address'.tr,
            isTextArea: true,
            textController: controller.addressTextController,
            fieldName: kFieldaddress,
          ),
          Obx(() {
            if (controller.state.value != ControllerState.loading &&
                controller.listStatus.isNotEmpty) {
              return _dropDownWrapper(
                context,
                userData: userData,
                label: 'label_form_marital'.tr,
                item: controller.listStatus,
                selectedItem: controller.getMaritalStatusDisplayValue(
                  controller.maritalTextController.text,
                ),
                enabled: controller.isEdit.isTrue,
                onChanged: (val) {
                  controller.maritalTextController.text = val;
                },
                fieldName: kFieldmaritalStatus,
              );
            }
            return Utils.shimmerLoad();
          }),
          Obx(() {
            if (controller.state.value != ControllerState.loading &&
                controller.listPendidikan.isNotEmpty) {
              return _dropDownWrapper(
                context,
                userData: userData,
                label: 'label_form_education'.tr,
                item: controller.listPendidikan,
                selectedItem: controller.getEducationDisplayValue(
                  controller.educationTextController.text,
                ),
                enabled: controller.isEdit.isTrue,
                onChanged: (val) {
                  controller.educationTextController.text = val;
                },
                fieldName: kFieldeducation,
              );
            }
            return Utils.shimmerLoad();
          }),
        ] else ...[
          // Fields for other roles
          _textFieldWrapper(
            context,
            label: 'label_form_full_name'.tr,
            intialValue: 'label_form_full_name'.tr,
            textController: controller.fullNameTextController,
            fieldName: 'name',
            isRestricted: true,
          ),
          controller.userData.value.channel == kUserChannelBan
              ? _textFieldWrapper(
                context,
                label: 'label_form_agent_code'.tr,
                intialValue: 'label_form_agent_code'.tr,
                textController: controller.agentCodeTextController,
                fieldName: 'label_form_agent_code'.tr,
                isRestricted: true,
              )
              : _textFieldWrapper(
                context,
                label: 'Username',
                intialValue: 'Username',
                textController: controller.usernameTextController,
                fieldName: 'username',
                isRestricted: true,
              ),
          _textFieldWrapper(
            context,
            label: 'Channel',
            intialValue: 'Channel',
            textController: controller.channelTextController,
            fieldName: 'channel',
            isRestricted: true,
          ),
          _textFieldWrapper(
            context,
            label: 'position'.tr,
            intialValue: 'position'.tr,
            textController: controller.roleNameTextController,
            fieldName: 'roles.name',
            isRestricted: true,
          ),
          _textFieldWrapper(
            context,
            label: 'KCU / KCP / Cabang',
            intialValue: 'KCU / KCP / Cabang',
            textController: controller.branchNameTextController,
            fieldName: 'branchName',
            isRestricted: true,
          ),
          _textFieldWrapper(
            context,
            label: 'Status',
            intialValue: 'Status',
            textController: controller.statusTextController,
            fieldName: 'status',
            isRestricted: true,
          ),
        ],
        SizedBox(height: paddingLarge),
      ],
    );
  }

  Padding _dropDownWrapper(
    context, {
    required UserModels userData,
    required String label,
    required List<String> item,
    required String selectedItem,
    required bool enabled,
    required Function(String) onChanged,
    required String fieldName,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingMedium),
      child: Column(
        children: [
          PdlDropDown(
            title: label,
            item: item,
            selectedItem: selectedItem,
            enabled:
                controller.inApprovalList.contains(fieldName)
                    ? false
                    : controller.isEdit.value,
            //NOTE : uncomment when need show specific message approval status in field
            // borderColor:
            //     controller.inApprovalList.contains(fieldName)
            //         ? kColorError
            //         : null,
            onChanged: (val) => onChanged(val!),
          ),
          //NOTE : uncomment when need show specific message approval status in field
          // if (controller.inApprovalList.contains(fieldName))
          //   _changeInProgress(context),
        ],
      ),
    );
  }

  Padding _textFieldWrapper(
    context, {
    required String label,
    String? intialValue,
    required TextEditingController textController,
    bool? isRestricted,
    bool? isTextArea,
    TextInputType? keyboardType,
    bool? isPhoneNumber,
    required String fieldName,
  }) {
    if (isRestricted == true) {
      return Padding(
        padding: const EdgeInsets.only(bottom: paddingMedium),
        child: Column(
          children: [
            PdlTextField(
              label: label,
              textController: textController,
              hint: intialValue,
              enabled: false,
              isTextArea: isTextArea,
              keyboardType: keyboardType,
              isPhoneNumber: isPhoneNumber,
            ),
          ],
        ),
      );
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingMedium),
      child: Obx(
        () => Column(
          children: [
            PdlTextField(
              label: label,
              textController: textController,
              hint: intialValue,
              //NOTE : uncomment when need show specific message approval status in field
              // borderColor:
              //     controller.inApprovalList.contains(fieldName)
              //         ? kColorError
              //         : null,
              enabled:
                  controller.inApprovalList.contains(fieldName)
                      ? false
                      : controller.isEdit.value,
              isTextArea: isTextArea,
              keyboardType: keyboardType,
              isPhoneNumber: isPhoneNumber,
            ),
            //NOTE : uncomment when need show specific message approval status in field
            // if (controller.inApprovalList.contains(fieldName))
            //   _changeInProgress(context),
          ],
        ),
      ),
    );
  }
}
