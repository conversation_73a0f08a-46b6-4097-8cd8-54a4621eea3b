import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/agent_photo.dart';
import 'package:pdl_superapp/components/agent_profile_card.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/components/widget/graphic_widget.dart';
import 'package:pdl_superapp/controllers/widget/production/production_detail_widget_controller.dart';
import 'package:pdl_superapp/models/widget/widget_production_models.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

/// A page that displays production data for a specific agent (Direct BP).
///
/// This page shows production data in both monthly and yearly views
/// for a specific agent code passed as an argument.
class ProductionDetailWidgetPage extends StatelessWidget {
  ProductionDetailWidgetPage({super.key});

  final ProductionDetailWidgetController controller = Get.put(
    ProductionDetailWidgetController(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => BaseDetailPage(
        title: controller.getPageTitle(),
        backEnabled: true,
        controller: controller,
        onRefresh: () async => controller.onInit(),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: paddingMedium),
          child: Obx(
            () => Column(
              children: [
                SizedBox(height: paddingLarge),
                _header(context),
                SizedBox(height: paddingMedium),
                _buildAgentProfileCard(),
                SizedBox(height: paddingMedium),
                _buildGraphicWidget(context),
                SizedBox(height: paddingMedium),
                _buildDataContainer(context),
                SizedBox(height: paddingExtraLarge),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Returns the appropriate data container based on selected time period
  Widget _buildDataContainer(BuildContext context) {
    return Obx(() {
      final isMonthly = controller.selectedMonth.value == kSwitchMonthly;
      final gradientColors =
          isMonthly
              ? [Color(0xFF0083B0), Color(0xFF00B4DB)]
              : [Color(0xFF679047), Color(0xFF6FC36E)];
      final periodText =
          isMonthly
              ? 'Bulan ${DateFormat('MMMM').format(DateTime.now())} ${DateTime.now().year}'
              : 'Tahun ${DateTime.now().year}';

      return _buildDataContainerWithHeader(
        context,
        gradientColors: gradientColors,
        periodText: periodText,
      );
    });
  }

  /// Common method to build data container with header
  /// Reduces code duplication between monthly and yearly views
  Container _buildDataContainerWithHeader(
    BuildContext context, {
    required List<Color> gradientColors,
    required String periodText,
  }) {
    return Container(
      width: Get.width,
      decoration: BoxDecoration(
        border: Border.all(
          color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Obx(() {
        // Get current filtered data based on selected period
        final dataToShow = controller.getCurrentFilteredData();

        return Column(
          children: [
            _buildHeaderContainer(
              context,
              periodText: periodText,
              gradientColors: gradientColors,
            ),
            _buildSearchField(context),
            _buildDataList(context, dataToShow),
          ],
        );
      }),
    );
  }

  /// Builds the header container with gradient background
  Container _buildHeaderContainer(
    BuildContext context, {
    required String periodText,
    required List<Color> gradientColors,
  }) {
    String selectTitle = "";
    switch (controller.selectedType.value) {
      case kSwitchProdGroup:
        selectTitle = 'group_policy_str'.tr;
        break;
      case kSwitchProdTeam:
        selectTitle = 'team_policy_str'.tr;
        break;
      default:
        selectTitle = 'individual_policy_str'.tr;
    }
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$selectTitle $periodText',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: kColorBorderLight,
              fontWeight: FontWeight.w700,
            ),
          ),
          _buildNetApeText(context),
        ],
      ),
    );
  }

  /// Builds the NET APE text with reactive total
  Widget _buildNetApeText(BuildContext context) {
    return Obx(() {
      // final netApeTotal = controller.getCurrentTotalNetApe();

      return RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: 'NET APE ',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: kColorBorderLight),
            ),
            TextSpan(
              text:
                  'Rp${Utils.currencyFormatters(data: controller.totalNetApe.value.toString(), currency: '')}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: kColorBorderLight,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// Builds the search field
  Widget _buildSearchField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(paddingMedium),
      child: PdlTextField(
        hint: 'Cari nama, kode agen atau nomor polis',
        onChanged: (value) => controller.onSearchTextChanged(value),
        prefixIcon: Padding(
          padding: EdgeInsets.all(paddingMedium),
          child: Utils.cachedSvgWrapper(
            'icon/ic-linear-search -2.svg',
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
      ),
    );
  }

  /// Builds the data list with appropriate card type
  Widget _buildDataList(
    BuildContext context,
    List<WidgetProductionDetailModels> dataToShow,
  ) {
    return Column(
      children: [
        if (controller.state.value == ControllerState.loading)
          Padding(
            padding: EdgeInsets.only(bottom: paddingMedium),
            child: CircularProgressIndicator(),
          ),
        if (controller.state.value != ControllerState.loading &&
            dataToShow.isEmpty)
          Padding(
            padding: EdgeInsets.only(bottom: paddingMedium),
            child: Utils.cachedSvgWrapper(
              'icon/illustration-empty-doc-check.svg',
              width: Get.width / 2,
            ),
          ),
        for (int i = 0; i < dataToShow.length; i++)
          // _isIndividualPolicyData(dataToShow[i])
          //     // ? _contentCard(context, data: dataToShow[i], index: i, totalItems: dataToShow.length)
          //     :
          _teamContentCard(
            context,
            data: dataToShow[i],
            index: i,
            totalItems: dataToShow.length,
          ),
      ],
    );
  }

  /// Determines if the data represents individual policy data or team/agent data
  // bool _isIndividualPolicyData(WidgetProductionDetailModels data) {
  //   // If it has policy information, it's individual policy data
  //   return (data.policyHolderName != null &&
  //           data.policyHolderName!.isNotEmpty) ||
  //       (data.policyNo != null && data.policyNo!.isNotEmpty);
  // }

  // /// Builds a card for individual policy data
  // Widget _contentCard(
  //   BuildContext context, {
  //   required WidgetProductionDetailModels data,
  //   required int index,
  //   required int totalItems,
  // }) {
  //   return Container(
  //     padding: EdgeInsets.symmetric(horizontal: paddingMedium),
  //     margin: EdgeInsets.only(top: paddingSmall),
  //     width: Get.width,
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Row(
  //           children: [
  //             Expanded(
  //               child: Text(
  //                 data.policyHolderName ?? '-',
  //                 style: Theme.of(
  //                   context,
  //                 ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
  //                 overflow: TextOverflow.ellipsis,
  //               ),
  //             ),
  //             _buildStatusBadge(context, data.status),
  //           ],
  //         ),
  //         Text(
  //           data.policyNo ?? '-',
  //           style: Theme.of(context).textTheme.bodySmall?.copyWith(height: 2),
  //         ),
  //         _buildNetApeAmount(context, data.netApe, isSmall: true),
  //         SizedBox(height: paddingSmall),
  //         _buildDividerIfNeeded(index, totalItems),
  //       ],
  //     ),
  //   );
  // }

  /// Builds a card for team/group data
  Widget _teamContentCard(
    BuildContext context, {
    required WidgetProductionDetailModels data,
    required int index,
    required int totalItems,
  }) {
    // For detail page, we don't need clickable functionality since this is already a detail view
    // But we keep the same visual structure as the main production page
    return InkWell(
      onTap:
          () =>
              (controller.selectedType.value != kSwitchProdIndividu
                  ? _navigateToDetailPage(data)
                  : null),
      child: Container(
        color: Colors.transparent,
        padding: EdgeInsets.symmetric(horizontal: paddingMedium),
        margin: EdgeInsets.only(top: paddingSmall),
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (controller.selectedType.value != kSwitchProdIndividu)
                  AgentPhotoFactory.medium(
                    photoUrl: data.agentPhoto,
                    agentName: data.agentName,
                  ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w700),
                          children: [
                            TextSpan(
                              text:
                                  (controller.selectedType.value ==
                                          kSwitchProdIndividu
                                      ? data.policyHolderName
                                      : '${data.agentLevel} ${data.agentName ?? '-'}'),
                            ),
                            TextSpan(
                              style: Theme.of(context).textTheme.bodyMedium,
                              text:
                                  (controller.userLevel.value.inList([
                                            kUserLevelBd,
                                            kUserLevelBm,
                                          ]) &&
                                          controller
                                                  .agentProfile
                                                  .value
                                                  ?.agentName ==
                                              data.agentName)
                                      ? " (Saya)"
                                      : "",
                            ),
                          ],
                        ),
                      ),
                      Text(
                        (controller.selectedType.value == kSwitchProdIndividu
                            ? data.policyNo ?? '-'
                            : data.agentCode ?? '-'),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color:
                              Get.isDarkMode
                                  ? kColorTextTersier
                                  : kColorTextTersierLight,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      _buildNetApeAmount(context, data.netApe),
                    ],
                  ),
                ),
                if (controller.selectedType.value != kSwitchProdIndividu)
                  Icon(
                    Icons.chevron_right,
                    color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                  ),
                if (controller.selectedType.value == kSwitchProdIndividu)
                  _buildStatusBadge(context, data.status),
              ],
            ),
            _buildDividerIfNeeded(index, totalItems),
          ],
        ),
      ),
    );
  }

  /// Builds a status badge with appropriate colors
  Widget _buildStatusBadge(BuildContext context, String? status) {
    final isActive = ['aktif', 'active'].contains(status?.toLowerCase());
    final backgroundColor =
        isActive ? kColorGlobalBgGreen : kColorGlobalBgWarning;
    final textColor = isActive ? kColorGlobalGreen : kColorGlobalWarning;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: paddingSmall, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        status ?? '-',
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(color: textColor),
      ),
    );
  }

  void _navigateToDetailPage(WidgetProductionDetailModels data) {
    if (data.agentCode == null) return;
    String teamType = controller.selectedType.value;
    if (controller.userChannel.value == "BAN") {
      switch (controller.selectedType.value) {
        case kSwitchProdGroup:
          teamType = kSwitchProdTeam;
          break;
        case kSwitchProdTeam:
          teamType = kSwitchProdIndividu;
          break;
        default:
          teamType = kSwitchProdGroup;
          break;
      }
      if (teamType == kSwitchProdIndividu) {
        debugPrint(
          "reloadController: ${controller.selectedType.value} agentCode: ${data.agentCode}",
        );
        controller.agentCode.value = data.agentCode!;
        controller.selectedType.value = teamType;
        controller.getData();
        return;
      }
    }
    debugPrint(
      "_navigateToDetailPage: ${controller.selectedType.value} agentCode: ${data.agentCode}",
    );
    Get.toNamed(
      '${Routes.PRODUCTION_DETAIL}?'
      'agentCode=${Uri.encodeComponent(data.agentCode!)}&'
      'productionType=${Uri.encodeComponent(controller.selectedMonth.value)}&'
      'productionTeamType=${Uri.encodeComponent(teamType)}',
    );
  }

  /// Builds a status badge with appropriate colors
  // Widget _buildStatusBadge(BuildContext context, String? status) {
  //   final isActive = status?.toLowerCase() == 'aktif';
  //   final backgroundColor =
  //       isActive ? kColorGlobalBgGreen : kColorGlobalBgWarning;
  //   final textColor = isActive ? kColorGlobalGreen : kColorGlobalWarning;

  //   return Container(
  //     padding: EdgeInsets.symmetric(horizontal: paddingSmall, vertical: 4),
  //     decoration: BoxDecoration(
  //       color: backgroundColor,
  //       borderRadius: BorderRadius.circular(16),
  //     ),
  //     child: Text(
  //       status ?? '-',
  //       style: Theme.of(
  //         context,
  //       ).textTheme.bodyMedium?.copyWith(color: textColor),
  //     ),
  //   );
  // }

  /// Builds the NET APE amount text
  Widget _buildNetApeAmount(
    BuildContext context,
    num? amount, {
    bool isSmall = false,
  }) {
    final formattedAmount = Utils.currencyFormatters(
      data: ((amount ?? 0).toString()),
      currency: '',
    );

    return Text(
      'NET APE $formattedAmount',
      style: (isSmall
              ? Theme.of(context).textTheme.bodySmall
              : Theme.of(context).textTheme.bodyMedium)
          ?.copyWith(
            fontWeight: FontWeight.w700,
            height: isSmall ? 1 : null,
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
          ),
      overflow: TextOverflow.ellipsis,
    );
  }

  /// Builds a divider if not the last item
  Widget _buildDividerIfNeeded(int index, int totalItems) {
    return index != (totalItems - 1)
        ? Divider()
        : SizedBox(height: paddingSmall);
  }

  /// Builds the graphic widget for production charts
  Widget _buildGraphicWidget(BuildContext context) {
    return Obx(() {
      // Only show graphic if agentCode is available
      if (controller.agentCode.value.isEmpty) {
        return const SizedBox.shrink();
      }

      return SizedBox(
        width: Get.width,
        child: GraphicWidget(
          agentCode: controller.agentCode.value,
          initialFilterType: controller.selectedMonth.value,
          userLevel: controller.userLevel.value,
          userChannel: controller.userChannel.value,
          selectedType: controller.selectedType.value,
        ),
      );
    });
  }

  /// Builds the agent profile card
  Widget _buildAgentProfileCard() {
    return Obx(() {
      return AgentProfileCard(
        agentProfile: controller.agentProfile.value,
        isLoading:
            controller.isLoading.value && controller.agentProfile.value == null,
      );
    });
  }

  /// Builds the header with title and period dropdown
  Widget _header(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Row(
        children: [
          Expanded(child: TitleWidget(title: controller.getPageTitle())),
          _buildPeriodDropdown(context),
        ],
      ),
    );
  }

  /// Builds the period dropdown (monthly/yearly)
  Widget _buildPeriodDropdown(BuildContext context) {
    // Define dropdown theme
    final dropdownTheme = Theme.of(context).copyWith(
      inputDecorationTheme: const InputDecorationTheme(
        errorBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        focusedErrorBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
        isDense: true,
      ),
    );

    // Define dropdown decoration
    final dropdownDecoration = CustomDropdownDecoration(
      closedBorderRadius: BorderRadius.circular(8),
      expandedBorderRadius: BorderRadius.circular(8),
      closedFillColor: Theme.of(context).colorScheme.surface,
      expandedFillColor: Theme.of(context).colorScheme.surface,
      closedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      expandedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      listItemStyle: Theme.of(context).textTheme.bodyMedium,
      hintStyle: Theme.of(context).textTheme.bodyMedium,
      headerStyle: Theme.of(context).textTheme.bodyMedium,
    );

    return Theme(
      data: dropdownTheme,
      child: SizedBox(
        width: 150,
        child: CustomDropdown(
          items: const ['Perbulan', 'Pertahun'],
          initialItem:
              controller.selectedMonth.value == kSwitchMonthly
                  ? "Perbulan"
                  : "Pertahun",
          onChanged: _handlePeriodChange,
          closedHeaderPadding: EdgeInsets.all(paddingSmall),
          decoration: dropdownDecoration,
        ),
      ),
    );
  }

  /// Handles period dropdown change
  void _handlePeriodChange(String? val) {
    if (val == null) return;

    final newPeriod =
        val.toLowerCase() == 'perbulan' ? kSwitchMonthly : kSwitchYearly;

    // Only change if it's different from current selection
    if (controller.selectedMonth.value != newPeriod) {
      controller.selectedMonth.value = newPeriod;
    }
  }
}
