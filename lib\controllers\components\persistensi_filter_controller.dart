import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/branch_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PersistensiFilterController extends BaseControllers {
  RxList<BranchModels> branches = <BranchModels>[].obs;
  late SharedPreferences prefs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    await loadBranches();
  }

  // Load branches for filter
  Future<void> loadBranches() async {
    setLoading(true);
    
    try {
      final areaCode = prefs.getString(kStorageAreaCode) ?? '';
      final mbranchCode = prefs.getString(kStorageMBranchCode) ?? '';
      final channel = prefs.getString(kStorageUserChannel) ?? '';
      
      // Build parameters based on available data
      String params;
      if (areaCode.isNotEmpty && areaCode != '-') {
        // Use areaCode if available
        params = 'areaCode=$areaCode&channel=$channel&size=100';
      } else if (mbranchCode.isNotEmpty && mbranchCode != '-') {
        // Fallback to branchCode if areaCode is not available
        params = 'branchCode=$mbranchCode&channel=$channel&size=100';
      } else {
        // No valid parameters available
        branches.clear();
        setLoading(false);
        return;
      }
      
      await api.getBranchList(
        controllers: this,
        params: params,
        code: kReqGetBranchList,
      );
    } catch (e) {
      branches.clear();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    
    if (requestCode == kReqGetBranchList) {
      _parseBranchData(response);
    }
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    branches.clear();
    setLoading(false);
  }

  void _parseBranchData(response) {
    try {
      if (response != null && response['content'] != null) {
        final List content = response['content'];
        branches.assignAll(
          content.map((e) => BranchModels.fromJson(e)).toList(),
        );
      } else {
        branches.clear();
      }
    } catch (e) {
      branches.clear();
    }
  }
}
