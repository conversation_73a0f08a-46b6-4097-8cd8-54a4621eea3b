import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_dialog_content.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class BottomSheetRejoin {
  static confirmRejoin(
    BuildContext context, {
    required VoidCallback onTap,
    required String selectedLevelProposed,
  }) {
    PdlBaseDialog(
      context: context,
      child: PdlDialogContent(
        textButtonPos: 'yes'.tr,
        message:
            '${('are_you_sure_want_to_apply_for_bp_rejoining'.tr).replaceAll('{level}', selectedLevelProposed)}?',
        onTap: () {
          Get.back();
          confirmChangePolis(context, onTap: onTap);
        },
      ),
    );
  }

  static confirmChangePolis(
    BuildContext context, {
    required VoidCallback onTap,
  }) {
    PdlBaseDialog(
      context: context,
      child: PdlDialogContent(
        textButtonPos: 'label_yes_sure'.tr,
        icon: Utils.cachedSvgWrapper('icon/ic-dialog-exclamation.svg'),
        message:
            'with_a_request_for_the_transfer_of_this_policy_maintenance'.tr,
        onTap: () {
          Get.back();
          onTap();
        },
      ),
    );
  }

  static cancelRejoin(
    BuildContext context, {
    required Function(String reason) onTap,
  }) {
    String reason = '';
    PdlBaseDialog(
      child: Column(
        children: [
          Utils.cachedSvgWrapper(
            'icon/ic-dialog-question.svg',
            width: 60,
            height: 60,
          ),
          SizedBox(height: paddingMedium),
          Text(
            'label_cancel_application'.tr,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: paddingLarge),
          PdlTextField(
            hint: 'label_input_reason_cancel'.tr,
            label: 'title_cancel_termination'.tr,
            maxLength: 120,
            height: 100,
            isTextArea: true,
            onChanged: (p0) => reason = p0,
            keyboardType: TextInputType.multiline,
          ),
          SizedBox(height: paddingLarge),
          SizedBox(
            width: Get.width,
            child: Row(
              children: [
                Expanded(
                  child: FilledButton(
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all(
                        Colors.transparent,
                      ),
                      side: WidgetStateProperty.all(
                        BorderSide(color: Color(0xFFD1D1D1)),
                      ),
                      foregroundColor: WidgetStateProperty.all(
                        Color(0xFF0C9DEB),
                      ),
                      padding: WidgetStateProperty.all(
                        EdgeInsets.symmetric(horizontal: paddingSmall),
                      ),
                    ),
                    child: Text(
                      'label_cancel'.tr,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    onPressed: () => Get.back(),
                  ),
                ),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: FilledButton(
                    onPressed: () {
                      if (reason.isEmpty) {
                        Get.snackbar('Oops', 'please_fill_reason_cancel'.tr);
                        return;
                      }
                      Get.back();
                      onTap(reason);
                    },
                    child: Text('yes'.tr),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      context: context,
    );
  }
}
