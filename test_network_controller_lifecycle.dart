// Test file untuk validasi NetworkController app lifecycle fix
// Simpan di folder test/ atau gunakan untuk manual testing

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/network_controller.dart';

class NetworkControllerTestPage extends StatefulWidget {
  @override
  _NetworkControllerTestPageState createState() => _NetworkControllerTestPageState();
}

class _NetworkControllerTestPageState extends State<NetworkControllerTestPage> {
  late NetworkController networkController;

  @override
  void initState() {
    super.initState();
    try {
      networkController = Get.find<NetworkController>();
    } catch (e) {
      networkController = Get.put(NetworkController());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Network Controller Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Display
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Network Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Obx(() => Row(
                      children: [
                        Icon(
                          networkController.isConnected.value 
                            ? Icons.wifi 
                            : Icons.wifi_off,
                          color: networkController.isConnected.value 
                            ? Colors.green 
                            : Colors.red,
                        ),
                        SizedBox(width: 8),
                        Text(
                          networkController.isConnected.value 
                            ? 'Connected' 
                            : 'Disconnected',
                          style: TextStyle(
                            color: networkController.isConnected.value 
                              ? Colors.green 
                              : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    )),
                    SizedBox(height: 8),
                    Text(
                      'Dialog Shown: ${networkController.hasShownDisconnectedDialog}',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Test Instructions
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Testing Instructions',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '1. Background/Foreground Test:\n'
                      '   • Press home button to background app\n'
                      '   • Wait 5-10 seconds\n'
                      '   • Return to app\n'
                      '   • Should NOT show offline dialog\n\n'
                      '2. Real Disconnection Test:\n'
                      '   • Turn off WiFi/mobile data\n'
                      '   • Should show offline dialog\n'
                      '   • Turn on connection\n'
                      '   • Should show reconnection notification\n\n'
                      '3. Grace Period Test:\n'
                      '   • Use Debug Status button\n'
                      '   • Check grace period status after resume',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Test Buttons
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Actions',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16),
                    
                    // Debug Status Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          networkController.debugStatus();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Debug status printed to console'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                        child: Text('Debug Status'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                        ),
                      ),
                    ),
                    
                    SizedBox(height: 8),
                    
                    // Force Check Connection Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () async {
                          await networkController.forceCheckConnection();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Force connection check completed'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                        child: Text('Force Check Connection'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                        ),
                      ),
                    ),
                    
                    SizedBox(height: 8),
                    
                    // Retry Connection Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          networkController.retry();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Connection retry initiated'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                        child: Text('Retry Connection'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                        ),
                      ),
                    ),
                    
                    SizedBox(height: 8),
                    
                    // Reset Dialog State Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          networkController.resetDialogState();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Dialog state reset'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                        child: Text('Reset Dialog State'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Expected Behavior
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Expected Behavior',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '✅ No offline dialog when returning from background\n'
                      '✅ Offline dialog appears for real disconnections\n'
                      '✅ Reconnection notification when connection restored\n'
                      '✅ Grace period active for 3 seconds after app resume\n'
                      '✅ Normal behavior after grace period ends',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Helper untuk menambahkan route testing (opsional)
class NetworkTestRoute {
  static const String name = '/network-test';
  
  static GetPage get route => GetPage(
    name: name,
    page: () => NetworkControllerTestPage(),
  );
}

// Usage: Tambahkan ke app_routes.dart atau gunakan Get.to()
// Get.to(() => NetworkControllerTestPage());
