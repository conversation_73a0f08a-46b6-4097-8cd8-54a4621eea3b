class ValidasiBanModel {
  final String agentCode;
  final String agentName;
  final String agentPhoto;
  final String agentLevel;
  final List<ValidasiBanPeriode> individu;
  final List<ValidasiBanTeam> team;

  ValidasiBanModel({
    required this.agentCode,
    required this.agentN<PERSON>,
    required this.agentPhoto,
    required this.agentLevel,
    required this.individu,
    required this.team,
  });

  factory ValidasiBanModel.fromJson(Map<String, dynamic> json) {
    return ValidasiBanModel(
      agentCode: json['agentCode'] ?? '',
      agentName: json['agentName'] ?? '',
      agentPhoto: json['agentPhoto'] ?? '',
      agentLevel: json['agentLevel'] ?? '',
      individu:
          (json['individu'] as List<dynamic>?)
              ?.map((item) => ValidasiBanPeriode.fromJson(item))
              .toList() ??
          [],
      team:
          (json['team'] as List<dynamic>?)
              ?.map((item) => ValidasiBanTeam.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'agentCode': agentCode,
      'agentName': agentName,
      'agentPhoto': agentPhoto,
      'agentLevel': agentLevel,
      'result': individu,
      'team': team,
    };
  }
}

class ValidasiBanPeriode {
  final String periode;
  final String abu;

  ValidasiBanPeriode({required this.periode, required this.abu});

  factory ValidasiBanPeriode.fromJson(Map<String, dynamic> json) {
    return ValidasiBanPeriode(
      periode: json['periode'] ?? '',
      abu: json['abu'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {'periode': periode, 'abu': abu};
    return data;
  }
}

class ValidasiBanTeam {
  final String agentCode;
  final String agentName;
  final String agentPhoto;
  final String agentLevel;
  final List<ValidasiBanPeriode> result;

  ValidasiBanTeam({
    required this.agentCode,
    required this.agentName,
    required this.agentPhoto,
    required this.agentLevel,
    required this.result,
  });

  factory ValidasiBanTeam.fromJson(Map<String, dynamic> json) {
    return ValidasiBanTeam(
      agentCode: json['agentCode'] ?? '',
      agentName: json['agentName'] ?? '',
      agentPhoto: json['agentPhoto'] ?? '',
      agentLevel: json['agentLevel'] ?? '',
      result:
          (json['result'] as List<dynamic>?)
              ?.map((item) => ValidasiBanPeriode.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'agentCode': agentCode,
      'agentName': agentName,
      'agentPhoto': agentPhoto,
      'agentLevel': agentLevel,
      'result': result,
    };
    return data;
  }
}
