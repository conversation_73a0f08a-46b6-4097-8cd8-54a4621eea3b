import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';

import 'package:pdl_superapp/models/draggable_widget_model.dart';
import 'package:pdl_superapp/models/widget_item_model.dart';
import 'package:pdl_superapp/models/widget_api_model.dart';
import 'package:pdl_superapp/utils/favorite_widget_utils.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FavoriteWidgetController extends BaseControllers {
  FavoriteWidgetController();

  late SharedPreferences prefs;

  // Lists to store favorite and available widgets
  final RxList<WidgetItemModel> favoriteWidgets = <WidgetItemModel>[].obs;
  final RxList<WidgetItemModel> availableWidgets = <WidgetItemModel>[].obs;

  // Track original favorites to detect changes
  final RxList<String> originalFavoriteIds = <String>[].obs;

  // Flag to track if changes have been made
  final RxBool hasChanges = false.obs;

  // API widget data for filtering
  final RxList<WidgetApiModel> apiWidgets = <WidgetApiModel>[].obs;
  final RxList<String> availableWidgetKeys = <String>[].obs;

  // Storage key for saving favorite widgets
  static const String kStorageFavoriteWidgets = 'favorite_widgets';

  // Maximum number of favorite widgets allowed
  static const int maxFavoriteWidgets = 999;

  // Flag to prevent multiple simultaneous loads
  bool _isLoadingWidgets = false;

  String userChannel = 'AGE';

  // Request code for widget sort API
  static const int kReqGetWidgetSort = 100;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();

    userChannel = prefs.getString(kStorageUserChannel) ?? 'AGE';

    // Load widget sort data from API first
    await loadWidgetSortData();
  }

  // Load widget sort data from API
  Future<void> loadWidgetSortData() async {
    setLoading(true);
    try {
      String channel = prefs.getString(kStorageUserChannel) ?? '';
      await api.getWidgetSort(
        controllers: this,
        code: kReqGetWidgetSort,
        channel: channel,
      );
    } catch (e) {
      log('Error loading widget sort data: $e');
      // If API fails, still load widgets with user role filtering only
      await loadWidgets();
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqGetWidgetSort:
        log('Widget sort API success, parsing widget data');
        parseWidgetSortData(response);
        break;
      default:
        log('Unknown request code: $requestCode');
    }
  }

  @override
  void loadFailed({
    required int requestCode,
    required Response<dynamic> response,
  }) {
    super.loadFailed(requestCode: requestCode, response: response);

    switch (requestCode) {
      case kReqGetWidgetSort:
        log('Widget sort API failed, loading widgets without API filter');
        // If API fails, still load widgets with user role filtering only
        loadWidgets();
        break;
      default:
        log('Unknown request code failed: $requestCode');
    }
  }

  /// Parse widget sort data from API response
  Future<void> parseWidgetSortData(dynamic response) async {
    try {
      if (response is List) {
        // Parse the API response into WidgetApiModel objects
        apiWidgets.clear();
        availableWidgetKeys.clear();

        for (var item in response) {
          WidgetApiModel widget = WidgetApiModel.fromJson(item);
          if (widget.isActive) {
            apiWidgets.add(widget);
            availableWidgetKeys.add(widget.widgetKey);
          }
        }

        // Sort by ordering
        apiWidgets.sort((a, b) => a.ordering.compareTo(b.ordering));

        log('Parsed ${apiWidgets.length} active widgets from API');
        log('Available widget keys: ${availableWidgetKeys.join(', ')}');

        // Now load widgets with both user role and API filtering
        await loadWidgets();
      }
    } catch (e) {
      log('Error parsing widget sort data: $e');
      // If parsing fails, still load widgets with user role filtering only
      await loadWidgets();
    }
  }

  // Load widgets from Firestore first, then fallback to preferences
  Future<void> loadWidgets() async {
    // Prevent multiple simultaneous loads
    if (_isLoadingWidgets) {
      return;
    }

    _isLoadingWidgets = true;
    setLoading(true);

    try {
      // Create the full list of all available widgets
      List<WidgetItemModel> universeWidget = [
        WidgetItemModel(
          id: '1',
          label: 'customer_birthday_str'.tr,
          iconUrl: 'icon/widget-ulang-tahun-nasabah.svg',
          color: Colors.blue,
          widgetKey: kWidgetKeyUlangTahunNasabah,
        ),
        WidgetItemModel(
          id: '2',
          label: 'claim_status_str'.tr,
          iconUrl: 'icon/widget-status-klaim.svg',
          color: Colors.green,
          widgetKey: kWidgetKeyStatusKlaim,
        ),
        WidgetItemModel(
          id: '3',
          label: 'lapsed_policy_str'.tr,
          iconUrl: 'icon/widget-polis-lapse.svg',
          color: Colors.orange,
          widgetKey: kWidgetKeyPolisLapse,
        ),
        WidgetItemModel(
          id: '4',
          label: 'expired_policy_str'.tr,
          iconUrl: 'icon/widget-polis-jatuh-tempo.svg',
          color: Colors.red,
          widgetKey: kWidgetKeyPolisJatuhTempo,
        ),
        WidgetItemModel(
          id: '5',
          label: 'my_production_str'.tr,
          iconUrl: 'icon/widget-produksi-saya.svg',
          color: Colors.purple,
          widgetKey: kWidgetKeyProduksiSaya,
        ),
        WidgetItemModel(
          id: '6',
          label:
              (userChannel == "BAN"
                      ? 'validation_abu_system_str'
                      : 'validation_promotion_str')
                  .tr,
          iconUrl: 'icon/widget-validasi-promosi.svg',
          color: Colors.teal,
          widgetKey: kWidgetKeyValidasiPromosi,
        ),
        WidgetItemModel(
          id: '7',
          label: 'compensation_estimate_str'.tr,
          iconUrl: 'icon/widget-estimasi-kompensasi.svg',
          color: Colors.amber,
          widgetKey: kWidgetKeyEstimasiKompensasi,
        ),
        WidgetItemModel(
          id: '8',
          label: 'persistency_str'.tr,
          iconUrl: 'icon/widget-persistensi.svg',
          color: Colors.indigo,
          widgetKey: kWidgetKeyPersistensi,
        ),
        WidgetItemModel(
          id: '9',
          label: 'status_spaj_str'.tr,
          iconUrl: 'icon/widget-status-spaj.svg',
          color: Colors.cyan,
          widgetKey: kWidgetKeyStatusSpaj,
        ),
      ];

      var userRoles = (prefs.getStringList(kStorageUserRoles)) ?? [];
      List<WidgetItemModel> allWidgets = [];
      Set<String> addedWidgetKeys =
          <String>{}; // Track added widgetKeys to prevent duplicates

      // First filter by user roles
      List<WidgetItemModel> roleFilteredWidgets = [];
      for (var role in userRoles) {
        var findWidget = universeWidget.firstWhereOrNull(
          (element) => role == element.id,
        );
        if (findWidget != null &&
            !addedWidgetKeys.contains(findWidget.widgetKey)) {
          roleFilteredWidgets.add(findWidget);
          addedWidgetKeys.add(findWidget.widgetKey);
        }
      }

      // Then filter by API availability (only if API data is loaded)
      if (availableWidgetKeys.isNotEmpty) {
        log(
          'Filtering ${roleFilteredWidgets.length} role-filtered widgets with API data',
        );
        for (var widget in roleFilteredWidgets) {
          if (availableWidgetKeys.contains(widget.widgetKey)) {
            allWidgets.add(widget);
            log('Added widget: ${widget.label} (${widget.widgetKey})');
          } else {
            log(
              'Skipped widget: ${widget.label} (${widget.widgetKey}) - not available in API',
            );
          }
        }
        log('Final filtered widgets count: ${allWidgets.length}');
      } else {
        // If API data is not loaded, use all role-filtered widgets
        log('API data not loaded, using all role-filtered widgets');
        allWidgets.addAll(roleFilteredWidgets);
      }

      // Try to get favorite widget IDs from Firestore first
      List<String> favoriteIds = [];
      try {
        // Get FirestoreServices instance
        FirestoreServices firestoreServices;
        try {
          firestoreServices = Get.find<FirestoreServices>();
        } catch (e) {
          log('FirestoreServices not found, creating new instance');
          firestoreServices = Get.put(FirestoreServices());
        }

        // Get favorite widgets from Firestore
        List<String>? firestoreIds =
            await firestoreServices.getFavoriteWidgets();

        // If Firestore data is available, use it
        if (firestoreIds != null && firestoreIds.isNotEmpty) {
          log('Using favorite widgets from Firestore');
          favoriteIds = firestoreIds;

          // Also update SharedPreferences to keep them in sync
          await prefs.setStringList(kStorageFavoriteWidgets, favoriteIds);
        } else {
          // Fallback to SharedPreferences if Firestore data is not available
          log('Falling back to SharedPreferences for favorite widgets');
          favoriteIds = prefs.getStringList(kStorageFavoriteWidgets) ?? [];
        }
      } catch (e) {
        log('Error loading from Firestore, using SharedPreferences: $e');
        // Fallback to SharedPreferences if there's an error with Firestore
        favoriteIds = prefs.getStringList(kStorageFavoriteWidgets) ?? [];
      }

      // Store original favorites for comparison
      originalFavoriteIds.clear();
      originalFavoriteIds.addAll(favoriteIds);

      // Populate favorite widgets list
      favoriteWidgets.clear();
      Set<String> favoriteWidgetKeys =
          <String>{}; // Track added widgetKeys to prevent duplicates

      // Only populate favorite widgets if there are saved favorites
      if (favoriteIds.isNotEmpty) {
        for (String id in favoriteIds) {
          final widget = allWidgets.firstWhereOrNull(
            (widget) => widget.id == id,
          );

          // Only add if widget exists and widgetKey is not already added
          if (widget != null &&
              !favoriteWidgetKeys.contains(widget.widgetKey)) {
            favoriteWidgets.add(widget);
            favoriteWidgetKeys.add(widget.widgetKey);
          }
        }
      }
      // If favoriteIds is empty, favoriteWidgets will remain empty (allowing 0 favorites)

      // Populate available widgets list (all widgets) - already filtered for duplicates
      availableWidgets.clear();
      availableWidgets.addAll(allWidgets);

      // Remove any duplicates that might have slipped through
      _removeDuplicateWidgets();

      // Reset changes flag
      hasChanges.value = false;
    } catch (e) {
      Get.snackbar('Error', 'Failed to load widgets: $e');
    } finally {
      _isLoadingWidgets = false;
      setLoading(false);
    }
  }

  // Add a widget to favorites
  void addToFavorites(WidgetItemModel widget) {
    // Check if widget is already in favorites by ID or widgetKey
    if (favoriteWidgets.any(
      (item) => item.id == widget.id || item.widgetKey == widget.widgetKey,
    )) {
      return; // Widget already in favorites, do nothing
    }

    if (favoriteWidgets.length < maxFavoriteWidgets) {
      // Add to favorites
      favoriteWidgets.add(widget);

      // Set changes flag
      _checkForChanges();
    } else {
      Get.snackbar(
        'Maximum Reached',
        'You can only have $maxFavoriteWidgets favorite widgets',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Remove a widget from favorites
  void removeFromFavorites(WidgetItemModel widget) {
    // Remove from favorites by ID or widgetKey
    favoriteWidgets.removeWhere(
      (item) => item.id == widget.id || item.widgetKey == widget.widgetKey,
    );

    // Set changes flag
    _checkForChanges();
  }

  // Handle widget dropped on favorites area (general drop, not specific position)
  void onFavoriteDrop(DraggableWidget draggableWidget) {
    final widget = draggableWidget.widget;

    // If the widget is already in favorites and is being reordered
    if (draggableWidget.sourceType == SourceType.favorite) {
      // For general drop on favorites section, move to end of list
      int oldIndex = draggableWidget.sourceIndex;

      // Only move if not already at the end
      if (oldIndex != favoriteWidgets.length - 1) {
        // Remove from old position
        favoriteWidgets.removeAt(oldIndex);

        // Add to end of list
        favoriteWidgets.add(widget);

        log(
          'Moved widget ${widget.label} from index $oldIndex to end of favorites',
        );
      }
    } else {
      // Widget is coming from available widgets
      addToFavorites(widget);
    }

    // Set changes flag
    _checkForChanges();
  }

  // Handle widget dropped on available area
  void onAvailableDrop(DraggableWidget draggableWidget) {
    // Only remove if it's coming from favorites
    if (draggableWidget.sourceType == SourceType.favorite) {
      removeFromFavorites(draggableWidget.widget);
    }
  }

  // Handle widget dropped at specific position in favorites
  void onFavoriteDropAtPosition(
    DraggableWidget draggableWidget,
    int dropIndex,
  ) {
    final widget = draggableWidget.widget;

    // If the widget is from favorites (reordering)
    if (draggableWidget.sourceType == SourceType.favorite) {
      int oldIndex = draggableWidget.sourceIndex;

      // Skip if dropping at exactly the same position
      if (oldIndex == dropIndex) {
        return;
      }

      // Create a copy of the current list
      List<WidgetItemModel> newList = List.from(favoriteWidgets);

      // Remove the dragged item
      WidgetItemModel draggedItem = newList.removeAt(oldIndex);

      // Calculate the correct insertion index
      int insertIndex;

      if (oldIndex < dropIndex) {
        // Moving right: the target position shifts left by 1 after removal
        insertIndex = dropIndex - 1;

        // Special case: if we're moving to the immediate next position
        // we need to ensure it actually moves
        if (insertIndex == oldIndex) {
          insertIndex = oldIndex + 1;
        }
      } else {
        // Moving left: target position stays the same
        insertIndex = dropIndex;
      }

      // Clamp to valid range
      insertIndex = insertIndex.clamp(0, newList.length);

      // Insert the item at the new position
      newList.insert(insertIndex, draggedItem);

      // Update the observable list
      favoriteWidgets.clear();
      favoriteWidgets.addAll(newList);

      log(
        'Reordered widget ${widget.label} from index $oldIndex to $insertIndex (drop index: $dropIndex)',
      );
    } else {
      // Widget is coming from available widgets

      // Check if widget is already in favorites by ID or widgetKey
      if (favoriteWidgets.any(
        (item) => item.id == widget.id || item.widgetKey == widget.widgetKey,
      )) {
        return; // Widget already in favorites, do nothing
      }

      if (favoriteWidgets.length < maxFavoriteWidgets) {
        // Ensure the drop index is within bounds
        int insertIndex = dropIndex.clamp(0, favoriteWidgets.length);

        // Insert at specific position
        favoriteWidgets.insert(insertIndex, widget);

        log('Added widget ${widget.label} at position $insertIndex');
      } else {
        Get.snackbar(
          'Maximum Reached',
          'You can only have $maxFavoriteWidgets favorite widgets',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }
    }

    // Set changes flag
    _checkForChanges();
  }

  // Save favorite widget IDs to preferences and Firestore
  Future<void> saveFavorites() async {
    try {
      // Get the list of favorite widget IDs
      List<String> favoriteIds =
          favoriteWidgets.map((widget) => widget.id).toList();

      // Save to SharedPreferences (local storage)
      await prefs.setStringList(kStorageFavoriteWidgets, favoriteIds);

      // Update original favorites
      originalFavoriteIds.clear();
      originalFavoriteIds.addAll(favoriteIds);

      // Reset changes flag
      hasChanges.value = false;

      // Save to Firestore if online
      try {
        FirestoreServices firestoreServices;
        try {
          firestoreServices = Get.find<FirestoreServices>();
        } catch (e) {
          log('FirestoreServices not found, creating new instance');
          firestoreServices = Get.put(FirestoreServices());
        }
        await firestoreServices.saveFavoriteWidgets(favoriteIds);
      } catch (e) {
        log('Error saving to Firestore: $e');
        // Continue anyway as we've already saved to SharedPreferences
      }

      // Notify HomeController about favorite widget changes
      await FavoriteWidgetUtils.checkForFavoriteWidgetChanges();

      // Also force reload to ensure immediate update
      FavoriteWidgetUtils.forceReloadFavoriteWidgets();

      // Navigate back
      Get.back();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Gagal menyimpan pengaturan: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Check if there are changes compared to original
  void _checkForChanges() {
    List<String> currentIds =
        favoriteWidgets.map((widget) => widget.id).toList();

    // Different number of items means there are changes
    if (currentIds.length != originalFavoriteIds.length) {
      hasChanges.value = true;
      return;
    }

    // Check if all original IDs are in current IDs and in the same order
    for (int i = 0; i < originalFavoriteIds.length; i++) {
      if (i >= currentIds.length || originalFavoriteIds[i] != currentIds[i]) {
        hasChanges.value = true;
        return;
      }
    }

    hasChanges.value = false;
  }

  // Helper method to remove duplicates from any widget list based on widgetKey
  List<WidgetItemModel> _removeDuplicatesFromList(
    List<WidgetItemModel> widgets,
  ) {
    Set<String> seenWidgetKeys = <String>{};
    List<WidgetItemModel> uniqueWidgets = [];

    for (var widget in widgets) {
      if (!seenWidgetKeys.contains(widget.widgetKey)) {
        seenWidgetKeys.add(widget.widgetKey);
        uniqueWidgets.add(widget);
      }
    }

    return uniqueWidgets;
  }

  // Helper method to remove duplicates from favorite widgets based on widgetKey
  void _removeDuplicateWidgets() {
    List<WidgetItemModel> uniqueWidgets = _removeDuplicatesFromList(
      favoriteWidgets,
    );

    // Update the list if duplicates were found
    if (uniqueWidgets.length != favoriteWidgets.length) {
      favoriteWidgets.clear();
      favoriteWidgets.addAll(uniqueWidgets);
      _checkForChanges();
    }
  }

  // Public method to remove duplicates from favorite widgets
  void removeDuplicates() {
    _removeDuplicateWidgets();
  }

  // Public method to remove duplicates from available widgets
  void removeAvailableDuplicates() {
    List<WidgetItemModel> uniqueWidgets = _removeDuplicatesFromList(
      availableWidgets,
    );

    if (uniqueWidgets.length != availableWidgets.length) {
      availableWidgets.clear();
      availableWidgets.addAll(uniqueWidgets);
    }
  }

  // Refresh data method
  void refreshData() {
    // Only reload if not already loading
    if (!_isLoadingWidgets) {
      loadWidgetSortData();
    }
  }
}
