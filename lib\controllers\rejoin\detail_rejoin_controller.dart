import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/modal/choose_method_pick_image.dart';
import 'package:pdl_superapp/components/status_label.dart';
import 'package:pdl_superapp/models/approval_detail.dart';
import 'package:pdl_superapp/models/body/approval_body.dart';
import 'package:pdl_superapp/models/body/submit_rejoin_body.dart';
import 'package:pdl_superapp/models/response/detail_approval_response.dart';
import 'package:pdl_superapp/models/response/detail_rejoin_response.dart';
import 'package:pdl_superapp/models/response/eligible_candidates_response.dart';
import 'package:pdl_superapp/models/response/submit_rejoin_response.dart';
import 'package:pdl_superapp/models/response/upload_doc_rejoin_response.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class DetailRejoinController extends BaseControllers {
  Rx<File?> ktpImage = Rx<File?>(null);
  final maxKtpSize = 2048;
  final selectedKtpSize = Rxn<double>();
  final idApprovalHeader = Rxn<int>();
  final idRejoin = Rxn<int>();
  final isApprover = Rxn<bool>();
  final detailRejoinResp = Rxn<DetailRejoinResponse>();
  RxList<ApprovalDetail> historyApprovals = <ApprovalDetail>[].obs;

  final agentData = Rxn<EligibleCandidateModel>();
  final reasonRejoinCtrl = TextEditingController();
  final remarksApproval = TextEditingController();

  final selectedStatusApproval = 'Setuju'.obs;
  String currentUsername = '';
  String currentAgentCode = '';
  RxBool isAlreadyResponseApproval = false.obs;
  late SharedPreferences prefs;

  List<Map<String, dynamic>> levelProposeAvailables = [];
  RxBool isLoadingGetDetail = false.obs;
  RxString selectedLevelProposed = ''.obs;
  RxBool isOwn = false.obs;

  String baseUrl = ConfigReader.getBaseUrl();

  void getRejoinLevelAvailables() {
    if (agentData.value?.previousLevel == kLevelBP) {
      levelProposeAvailables = [
        {'code': 'BP', 'name': 'Business Partner'},
      ];
    } else if (agentData.value?.previousLevel == kLevelBM) {
      levelProposeAvailables = [
        {'code': 'BP', 'name': 'Business Partner'},
        {'code': 'BM', 'name': 'Business Manager'},
      ];
    } else {
      levelProposeAvailables = [
        {'code': 'BP', 'name': 'Business Partner'},
        {'code': 'BM', 'name': 'Business Manager'},
        {'code': 'BD', 'name': 'Business Developer'},
      ];
    }

    selectedLevelProposed.value = levelProposeAvailables.first['code'];
  }

  RxBool isValid = false.obs;

  void checkValid() {
    if (reasonRejoinCtrl.text.trim().isEmpty || ktpImage.value == null) {
      isValid.value = false;
    } else {
      isValid.value = true;
    }
  }

  @override
  Future<void> onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    idRejoin.value = Get.arguments['id_rejoin'];
    idApprovalHeader.value = Get.arguments['id_approval_header'];
    agentData.value = Get.arguments['agent'];
    isApprover.value = Get.arguments['as_approver'];
    currentUsername = prefs.getString(kStorageUserUsername) ?? '';
    currentAgentCode = prefs.getString(kStorageAgentCode) ?? '';
    isOwn.value = Get.arguments['is_own'] ?? false;

    getRejoinLevelAvailables();
    getDetailRejoin();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqSubmitRejoin:
        _successSubmitRejoin(response);
      case kReqListApprovalRejoin:
        _successGetListApproval(response);
      case kReqDetailRejoin:
        _successGetDetailRejoin(response);
      case kReqPostApproval:
      case kReqRequestCancelRejoin:
        getDetailRejoin();
      default:
    }
  }

  bool get showButtonApprove {
    if (detailRejoinResp.value?.status == Status.CANCELLED.name ||
        detailRejoinResp.value?.status == Status.COMPLETE.name ||
        detailRejoinResp.value?.status == Status.EXPIRED.name ||
        detailRejoinResp.value?.status == Status.DIKEMBALIKAN.name ||
        detailRejoinResp.value?.status == Status.REJECTED.name ||
        isApprover.value != true ||
        isAlreadyResponseApproval.value) {
      return false;
    }
    return true;
  }

  void _successGetDetailRejoin(response) {
    isLoadingGetDetail.value = false;
    try {
      final resp = DetailRejoinResponse.fromJson(response);
      detailRejoinResp.value = resp;
      agentData.value = EligibleCandidateModel(
        agentCode: resp.agentCode,
        agentName: resp.agentName,
        agentPicture: resp.agentPicture,
        branchAddress: resp.branch?.address,
        branchCity: resp.branch?.city,
        branchCode: resp.branch?.branchCode,
        branchName: resp.branch?.branchName,
        previousLeaderCode: resp.previousLeaderCode,
        previousLeaderName: '',
        previousLevel: resp.previousLevel,
      );
    } catch (e, st) {
      log('error success upload doc rejoin $e $st');
    }
  }

  void getDetailRejoin() {
    if (idRejoin.value != null) {
      reqGetDetailRejoin(id: idRejoin.value!);
    }

    if (idApprovalHeader.value != null) {
      reqGetListApproval();
    }
  }

  void _successGetListApproval(response) {
    try {
      final resp = DetailApprovalResponse.fromJson(response);
      historyApprovals.clear();
      historyApprovals.addAll(resp.approvalDetails ?? []);
      historyApprovals.refresh();

      isAlreadyResponseApproval.value = historyApprovals.any(
        (p0) =>
            p0.actionBy?.agentCode != null
                ? p0.actionBy?.agentCode == currentAgentCode
                : p0.actionBy?.username == currentUsername,
      );
    } catch (e, st) {
      log('error get list approval $e $st');
    }
  }

  void _successSubmitRejoin(response) {
    try {
      final resp = SubmitRejoinResponse.fromJson(response);
      idRejoin.value = resp.id;
      idApprovalHeader.value = resp.approvalHeader?.id;
      getDetailRejoin();
    } catch (e, st) {
      log('error success upload doc rejoin $e $st');
    }
  }

  void _successUploadKtp(response, {bool isRevise = false}) {
    try {
      final resp = UploadDocRejoinResponse.fromJson(response);

      final body = SubmitRejoinBody(
        agentCode: agentData.value?.agentCode ?? '',
        branchCode: agentData.value?.branchCode ?? '',
        proposedLeaderCode: agentData.value?.previousLeaderCode ?? '',
        proposedLevel: selectedLevelProposed.value,
        remarks: reasonRejoinCtrl.text,
        uploadedKtpPath: resp.initialPreview?.first ?? '',
      );

      if (isRevise) {
        reqPostReviseRejoin(body: body, id: '${idRejoin.value}');
      } else {
        reqPostRejoin(body: body);
      }
    } catch (e, st) {
      log('error success upload doc rejoin $e $st');
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    if (requestCode == kReqDetailRejoin) {
      isLoadingGetDetail.value = false;
    }
    toastError(
      title: 'Gagal',
      message:
          response.body['message'] ??
          '${response.body['error_description'] ?? 'Terjadi Kesalahan harap ulangi kembali'}',
    );
  }

  Future<void> downloadFile(String? urlString) async {
    if (urlString == null || urlString.isEmpty) {
      toastError(title: 'Error', message: 'No file URL available for download');
      return;
    }

    final Uri? uri = Uri.tryParse(urlString);
    if (uri == null) {
      toastError(title: 'Error', message: 'Invalid URL for download');
      return;
    }

    if (await canLaunchUrl(uri)) {
      try {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } catch (e) {
        toastError(title: 'Error', message: 'Could not launch URL: $e');
      }
    } else {
      toastError(
        title: 'Error',
        message:
            'Cannot launch URL. Please check if you have a browser or app to open this file type.',
      );
    }
  }

  Future<void> reqGetListApproval() async {
    setLoading(true);
    await api.getListApprovalRejoin(
      controllers: this,
      code: kReqListApprovalRejoin,
      id: '${idApprovalHeader.value}',
    );
  }

  Future<void> reqCancelRejoin({
    required int id,
    required String reason,
  }) async {
    setLoading(true);
    await api.putCancelRejoin(
      controllers: this,
      code: kReqRequestCancelRejoin,
      id: id,
      reason: reason,
    );
  }

  Future<void> reqGetDetailRejoin({
    bool isRefresh = false,
    required int id,
  }) async {
    setLoading(true);
    isLoadingGetDetail.value = true;
    await api.getDetailRejoin(
      controllers: this,
      code: kReqDetailRejoin,
      id: id,
    );
  }

  Future<void> reqPostRejoin({required SubmitRejoinBody body}) async {
    setLoading(true);
    await api.postRequestRejoin(
      controllers: this,
      code: kReqSubmitRejoin,
      data: body.toJson(),
    );
  }

  Future<void> reqPostReviseRejoin({
    required SubmitRejoinBody body,
    required String id,
  }) async {
    setLoading(true);
    await api.postReviseRejoin(
      controllers: this,
      code: kReqSubmitRejoin,
      data: body.toJson(),
      id: id,
    );
  }

  Future<void> reqPostUploadDocRejoin({bool isRevise = false}) async {
    if (ktpImage.value == null) {
      toastError(
        title: 'failed'.tr,
        message: 'please_select_ktp_photo_first'.tr,
      );
      return;
    }

    try {
      final form = FormData({
        'file': MultipartFile(
          ktpImage.value,
          filename: ktpImage.value!.path.split('/').last,
        ),
      });
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString(kStorageToken) ?? '';
      setLoading(true);
      final response = await GetConnect().post(
        '$baseUrl/agency/rejoin/upload',
        form,
        headers: {'Authorization': 'Bearer $token'},
        uploadProgress: (percent) {},
      );

      if (response.isOk) {
        setLoading(false);
        _successUploadKtp(response.body, isRevise: isRevise);
      } else {
        setLoading(false);
        toastError(
          title: 'Gagal',
          message: '${response.body['error_description'] ?? '-'}',
        );
      }
    } catch (e, st) {
      log('error upload doc rejoin $e $st');
    }
  }

  Future<void> reqPostApproval({required ApprovalBody body}) async {
    setLoading(true);
    await api.postApproval(
      controllers: this,
      code: kReqPostApproval,
      body: body,
    );
  }

  Future<void> pickKtpImage(BuildContext context) async {
    ChooseMethodPickImage().show(
      context,
      title: 'pick_ktp_photo_from'.tr,
      onSuccess: (file) async {
        final croppedImage = await Utils.cropKtpImage(
          context,
          imagePath: file.path,
        );
        if (croppedImage != null) {
          final selectedFile = File(croppedImage.path);
          final fileSize = await selectedFile.length();
          final sizeInMb = (fileSize / 1024 / 1024);
          if (sizeInMb <= (maxKtpSize / 1024)) {
            ktpImage.value = selectedFile;
            selectedKtpSize.value = sizeInMb;
            checkValid();
          } else {
            toastError(
              title: 'Error',
              message:
                  'File yang anda pilih berukuran ${sizeInMb.toStringAsFixed(2)}MB. Ukuran file tidak boleh lebih dari ${(maxKtpSize / 1024).toStringAsFixed(0)}MB',
            );
          }
        }
      },
      onError: (errorMessage) {},
    );
  }

  void toastError({required String title, required String message}) {
    Get.snackbar(
      title,
      message,
      colorText: Colors.white,
      backgroundColor: Colors.red,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void deleteSelectedKtp() {
    selectedKtpSize.value = null;
    ktpImage.value = null;
  }
}
