// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class NetworkController extends GetxController with WidgetsBindingObserver {
  final RxBool isConnected = true.obs;
  late StreamSubscription _subscription;

  // ignore: prefer_typing_uninitialized_variables
  var connectionChecker;

  // State management untuk dialog
  final RxBool _hasShownDisconnectedDialog = false.obs;
  // ignore: unused_field
  bool _wasConnectedBefore = true;

  // App lifecycle management
  bool _isAppInBackground = false;
  Timer? _resumeGracePeriodTimer;
  Timer? _debounceTimer;
  static const Duration _resumeGracePeriod = Duration(seconds: 5); // Increased to 5 seconds
  static const Duration _debounceDelay = Duration(milliseconds: 1500); // Debounce rapid changes

  // Configuration for grace period (can be adjusted if needed)
  static const bool _enableGracePeriod = true;
  static const bool _enableDebouncing = true;

  // Track last connection state to prevent rapid changes
  bool? _lastReportedState;
  DateTime? _lastStateChangeTime;

  @override
  Future<void> onInit() async {
    super.onInit();

    // Add app lifecycle observer
    if (!kIsWeb) {
      WidgetsBinding.instance.addObserver(this);
    }

    if (!kIsWeb) {
      connectionChecker = InternetConnectionChecker.createInstance();

      // Cek status koneksi awal
      try {
        bool initialStatus = await connectionChecker.hasConnection;
        isConnected.value = initialStatus;
        _wasConnectedBefore = initialStatus;
        // print('Initial connection status: $initialStatus');
      } catch (e) {
        // print('Error checking initial connection: $e');
        isConnected.value = false;
        _wasConnectedBefore = false;
      }

      // Setup listener untuk perubahan status
      _subscription = connectionChecker.onStatusChange.listen((
        InternetConnectionStatus status,
      ) {
        bool newConnectionStatus = status == InternetConnectionStatus.connected;
        // print(
        //   'Connection status changed: ${isConnected.value} -> $newConnectionStatus',
        // );

        // Handle disconnection - show dialog (with grace period check)
        if (isConnected.value && !newConnectionStatus) {
          // print('Showing disconnection dialog');
          _handleDisconnection();
        }

        // Handle reconnection - show success notification and dismiss dialog
        if (!isConnected.value && newConnectionStatus) {
          // print('Connection restored');
          _handleReconnection();
        }

        isConnected.value = newConnectionStatus;
      });
    } else {
      isConnected.value = true;
    }
  }

  @override
  void onClose() {
    if (!kIsWeb) {
      _subscription.cancel();
      WidgetsBinding.instance.removeObserver(this);
      _resumeGracePeriodTimer?.cancel();
    }
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.hidden:
        _isAppInBackground = true;
        // print('App went to background');
        break;
      case AppLifecycleState.resumed:
        if (_isAppInBackground) {
          // print('App resumed from background, starting grace period');
          _isAppInBackground = false;
          _startResumeGracePeriod();
        }
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  void _startResumeGracePeriod() {
    // Cancel any existing timer
    _resumeGracePeriodTimer?.cancel();

    // Start grace period timer
    _resumeGracePeriodTimer = Timer(_resumeGracePeriod, () {
      // Grace period ended, now we can show disconnection dialogs normally
      // print('Grace period ended, normal connection checking resumed');
    });
  }

  bool get _isInGracePeriod => _resumeGracePeriodTimer?.isActive ?? false;

  void _handleDisconnection() {
    // Skip showing disconnection dialog if we're in grace period after app resume
    if (_enableGracePeriod && _isInGracePeriod) {
      // print('Skipping disconnection dialog - app recently resumed from background');
      return;
    }
    _showDisconnectionDialog();
  }

  void _handleReconnection() {
    // Cancel grace period timer if connection is restored
    _resumeGracePeriodTimer?.cancel();
    _dismissDisconnectionDialog();
    _showConnectionRestoredNotification();
  }

  void retry() async {
    if (!kIsWeb && connectionChecker != null) {
      try {
        bool newStatus = await connectionChecker.hasConnection;
        // print('Retry connection check: $newStatus');

        // Update status dan trigger listener logic jika berubah
        if (newStatus == true) {
          // Simulate status change untuk trigger listener logic
          // if (!isConnected.value && newStatus) {
          // Reconnection
          _dismissDisconnectionDialog();
          _showConnectionRestoredNotification();
          // }
          isConnected.value = newStatus;
        }
      } catch (e) {
        // print('Error during retry: $e');
      }
    }
  }

  // /// Method untuk testing - simulasi disconnect
  // void simulateDisconnect() {
  //   print('Simulating disconnect');
  //   isConnected.value = false;
  //   _showDisconnectionDialog();
  // }

  /// Method untuk testing - simulasi reconnect
  // void simulateReconnect() {
  //   print('Simulating reconnect');
  //   isConnected.value = true;
  //   _showConnectionRestoredNotification();
  //   _dismissDisconnectionDialog();
  // }

  /// Debug method untuk cek status
  void debugStatus() {
    // print('=== NetworkController Debug Status ===');
    // print('isConnected: ${isConnected.value}');
    // print('hasShownDisconnectedDialog: ${_hasShownDisconnectedDialog.value}');
    // print('isAppInBackground: $_isAppInBackground');
    // print('isInGracePeriod: $_isInGracePeriod');
    // print('connectionChecker: $connectionChecker');
    // print('Get.context: ${Get.context}');
    // print('Get.isBottomSheetOpen: ${Get.isBottomSheetOpen}');
    // print('=====================================');
  }

  /// Method untuk force check koneksi (bypass grace period)
  Future<void> forceCheckConnection() async {
    if (!kIsWeb && connectionChecker != null) {
      try {
        bool currentStatus = await connectionChecker.hasConnection;
        // print('Force connection check result: $currentStatus');

        // Update status langsung tanpa melalui grace period
        if (isConnected.value != currentStatus) {
          if (currentStatus) {
            _handleReconnection();
          } else {
            _showDisconnectionDialog();
          }
          isConnected.value = currentStatus;
        }
      } catch (e) {
        // print('Error during force connection check: $e');
      }
    }
  }

  /// Cek apakah dialog disconnect sudah pernah ditampilkan dalam sesi ini
  bool get hasShownDisconnectedDialog => _hasShownDisconnectedDialog.value;

  /// Tandai bahwa dialog disconnect sudah ditampilkan
  void markDisconnectedDialogShown({bool? isShown}) {
    _hasShownDisconnectedDialog.value = isShown ?? true;
  }

  /// Reset state dialog ketika home di-refresh
  void resetDialogState() {
    _hasShownDisconnectedDialog.value = false;
  }

  /// Tampilkan dialog disconnection secara global
  void _showDisconnectionDialog() {
    // print('_showDisconnectionDialog called');

    // Cek apakah dialog sudah pernah ditampilkan dalam sesi ini
    if (_hasShownDisconnectedDialog.value) {
      // print('Dialog already shown, skipping');
      return;
    }

    if (Get.isBottomSheetOpen ?? false) {
      // print('Bottom sheet already open, skipping');
      return;
    }

    // tampilkan
    Utils().showNoConnectionDialog(this);

    // print('Marking dialog as shown and displaying');
    // Tandai bahwa dialog sudah ditampilkan
    markDisconnectedDialogShown();
  }

  /// Tutup dialog disconnection jika sedang terbuka
  void _dismissDisconnectionDialog() {
    if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    }
  }

  /// Tampilkan notifikasi ketika koneksi kembali
  void _showConnectionRestoredNotification() {
    markDisconnectedDialogShown(isShown: false);
    Utils.popup(
      body: 'Koneksi internet telah kembali normal',
      type: kPopupSuccess,
    );
  }
}
